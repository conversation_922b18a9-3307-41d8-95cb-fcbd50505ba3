#!/bin/bash

sudo yum clean metadata
sudo yum install -y java-22-amazon-corretto

# Set the default AWS region
export AWS_DEFAULT_REGION=us-west-2

## SETUP AND DOWNLOAD BUILD CACHE NODE JAR
export BUILD_CACHE_NODE_DIR=~/buildCacheNode
export BUILD_CACHE_NODE_JAR=${BUILD_CACHE_NODE_DIR}/buildCacheNode.jar
export BUILD_CACHE_NODE_DATA_DIR=${BUILD_CACHE_NODE_DIR}/data
export BUILD_CACHE_NODE_CONFIG_DIR=${BUILD_CACHE_NODE_DATA_DIR}/conf
export BUILD_CACHE_NODE_CONFIG_FILE=${BUILD_CACHE_NODE_CONFIG_DIR}/config.yaml
# SETUP PASSWORD INFORMATION
export BUILD_CACHE_NODE_PASSWORD_SECRET_NAME="gradle-build-cache-node-password"
export BUILD_CACHE_NODE_PASSWORD_FILE=${BUILD_CACHE_NODE_DIR}/password.txt

# Create Build Cache Node Directory
mkdir -p ${BUILD_CACHE_NODE_CONFIG_DIR}

# Download JAR
curl https://docs.gradle.com/develocity/build-cache-node/jar/build-cache-node-21.0.jar --output ${BUILD_CACHE_NODE_JAR}

# Save the password from environment variable to a file
export BUILD_CACHE_NODE_PASSWORD="$(aws secretsmanager get-secret-value --secret-id "${BUILD_CACHE_NODE_PASSWORD_SECRET_NAME}" --query SecretString --output text)"
echo "$BUILD_CACHE_NODE_PASSWORD" > ${BUILD_CACHE_NODE_PASSWORD_FILE}

# produces a salted hash response surrounded by quotes (BE CAREFUL WITH QUOTES)
export HASHED_PASSWORD="$(java -jar ${BUILD_CACHE_NODE_JAR} hash -p ${BUILD_CACHE_NODE_PASSWORD_FILE})"
sudo rm -rf "${BUILD_CACHE_NODE_PASSWORD_FILE}"

cat <<EOT > ${BUILD_CACHE_NODE_CONFIG_FILE}
version: 5
cache:
  targetSize:
    type: maxAvailable
  freeSpaceBufferSize: 2000
  maxArtifactSize: 150
  maxEntryAgeInHours: null
  accessControl:
    anonymousLevel: "none"
    users:
      unblocked:
        password: $HASHED_PASSWORD
        level: "readwrite"
        note: "Continuous Integration User"
uiAccess:
  type: "secure"
  username: "unblocked"
  password: $HASHED_PASSWORD
EOT

## SET UP SYSTEMD SERVICE
export BUILD_CACHE_NODE_SERVICE_NAME=buildCacheNode
export BUILD_CACHE_NODE_SCRIPT=/usr/bin/${BUILD_CACHE_NODE_SERVICE_NAME}.sh
export BUILD_CACHE_NODE_LIB_SERVICE=/lib/systemd/system/${BUILD_CACHE_NODE_SERVICE_NAME}.service
export BUILD_CACHE_NODE_ETC_SERVICE=/etc/systemd/system/${BUILD_CACHE_NODE_SERVICE_NAME}.service

sudo cat <<EOT > ${BUILD_CACHE_NODE_SCRIPT}
java -jar ${BUILD_CACHE_NODE_JAR} start --data-dir ${BUILD_CACHE_NODE_DATA_DIR} --port 80
EOT
sudo chmod +x ${BUILD_CACHE_NODE_SCRIPT}

sudo cat <<EOT > ${BUILD_CACHE_NODE_LIB_SERVICE}
[Unit]
Description=Gradle Build Cache Node Service.

[Service]
Type=simple
ExecStart=/bin/bash ${BUILD_CACHE_NODE_SCRIPT}

[Install]
WantedBy=multi-user.target
EOT
sudo cp ${BUILD_CACHE_NODE_LIB_SERVICE} ${BUILD_CACHE_NODE_ETC_SERVICE}
sudo chmod 644 ${BUILD_CACHE_NODE_ETC_SERVICE}
systemctl daemon-reload
sudo systemctl enable ${BUILD_CACHE_NODE_SERVICE_NAME}
sudo systemctl start ${BUILD_CACHE_NODE_SERVICE_NAME}
