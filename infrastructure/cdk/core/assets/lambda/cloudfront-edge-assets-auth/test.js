'use strict';
/*
 Unit tests to verify auth functions. Mostly consists of negative tests.
 Negative tests are used to ensure we don't accidentally allow to customer
 resources

 How to run:
    1- install dependencies `npm install`
    2- run local tests `npm test`
 */
const assert = require('assert').strict;
const https = require('https');
const lambdaLocal = require('lambda-local');
const path = require('path');
const testEventTemplate = require('./test-event.json');
const godModeTokenRequestHost = 'dev.getunblocked.com';
const godModeTokenRequestPath = '/api/godmode/858772';

describe('Test', function () {
    it('Missing auth header - expects 401', async function () {
        let testEvent = clone(testEventTemplate);
        delete testEvent.Records[0].cf.request.headers.authorization;
        assert.equal((await runLambda(testEvent)).status, undefined);
        assert.equal((await runLambda(testEvent)).uri, `/error-assets/error-401.png`);
    });

    it('Cors Preflight Request - expects 200', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers['access-control-request-headers'] = [
            {
                key: 'Access-Control-Request-Headers',
                value: 'Content-Type',
            },
        ];
        const result = await runLambda(testEvent);
        assert.equal(result.status, '200');
        assert.equal(result.headers['access-control-allow-origin'][0].value, '*');
        assert.equal(result.headers['access-control-allow-headers'][0].value, '*');
    });

    it('Expired token - expects 401', async function () {
        let testEvent = clone(testEventTemplate);
        // Template includes an expired token. No need to set anything here!
        assert.equal((await runLambda(testEvent)).status, '401');
    });

    it('Invalid token - expects 401', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.authorization[0].value = 'invalid token';
        testEvent.Records[0].cf.request.querystring = 'authorization=invalid-token';
        assert.equal((await runLambda(testEvent)).status, '401');
    });

    it('Invalid host - expects 500', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'buddybuild.com';
        assert.equal((await runLambda(testEvent)).status, '500');
    });

    it('Valid token with invalid uri - expects 400', async function () {
        for (let uri of ['/this/isinvalid', '/this/is/invalid/too', 'this/is/invalid/too/']) {
            const tokenResponse = await getToken();
            assert.equal(tokenResponse.status, 200);
            let testEvent = clone(testEventTemplate);
            testEvent.Records[0].cf.request.uri = uri;
            testEvent.Records[0].cf.request.headers.authorization[0].value = JSON.parse(tokenResponse['body']).token;
            assert.equal((await runLambda(testEvent)).status, '400');
        }
    });

    it('Valid token with unauthorized team id - expects 401', async function () {
        const tokenResponse = await getToken();
        assert.equal(tokenResponse.status, 200);
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri =
            '/assets/3A716A2B-84CA-4A5F-AE9F-AD920AF89C1F/123e4567-e89b-12d3-a456-426614174035';
        testEvent.Records[0].cf.request.headers.authorization[0].value = JSON.parse(tokenResponse['body']).token;
        assert.equal((await runLambda(testEvent)).status, '401');
    });

    it('Valid token and uri - expects 200', async function () {
        for (let uri of [
            '/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/123e4567-e89b-12d3-a456-426614174035',
            '///assets//f72bb2e8-61d0-4fb2-abb4-b406253e4e22//123e4567-e89b-12d3-a456-426614174035//',
        ]) {
            const tokenResponse = await getToken();
            assert.equal(tokenResponse.status, 200);
            let testEvent = clone(testEventTemplate);
            testEvent.Records[0].cf.request.uri = uri;
            testEvent.Records[0].cf.request.headers.authorization[0].value = JSON.parse(tokenResponse['body']).token;
            const result = await runLambda(testEvent);
            assert.equal(result.status, undefined);

            // Make sure `/assets` is removed to avoid cache misses
            assert.equal(result.uri.startsWith('/assets'), false);
        }
    });

    it('Valid cookie token and uri - expects 200', async function () {
        const tokenResponse = await getToken();
        assert.equal(tokenResponse.status, 200);
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.authorization = undefined;
        testEvent.Records[0].cf.request.uri =
            '/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/123e4567-e89b-12d3-a456-426614174035';
        testEvent.Records[0].cf.request.headers.cookie = [
            {
                key: 'Cookie',
                value: `authorization=${JSON.parse(tokenResponse['body']).token}`,
            },
        ];
        const result = await runLambda(testEvent);
        assert.equal(result.status, undefined);

        // Make sure `/assets` is removed to avoid cache misses
        assert.equal(result.uri.startsWith('/assets'), false);
    });
});

// Runs lambda function with provided event object
async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}

function getToken() {
    const params = {
        hostname: godModeTokenRequestHost,
        path: godModeTokenRequestPath,
        headers: {
            Accept: 'application/json',
        },
        port: 443,
        timeout: 1000,
    };

    return new Promise((resolve, reject) => {
        https
            .get(params, (resp) => {
                let result = {
                    status: resp.statusCode,
                    headers: resp.headers,
                    body: '',
                };
                resp.on('data', (chunk) => {
                    result.body += chunk;
                });
                resp.on('end', () => {
                    resolve(result);
                });
            })
            .on('error', (err) => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} : ${err.message}`);
                reject(err, null);
            });
    });
}
