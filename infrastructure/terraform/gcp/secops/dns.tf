# managed zone for the DNS name
resource "google_dns_managed_zone" "secops" {
  name        = "secops"
  dns_name    = "${var.dns_name}." # Must end with a dot
  description = "Managed zone for ${var.dns_name}"
}

# DNS records for domain listening on GKE Gateway
module "dns_records" {
  source       = "./modules/dns_records"
  subdomains   = var.test_hostnames
  managed_zone = google_dns_managed_zone.secops.name
  ip_address   = google_compute_global_address.gke_gateway.address
}

resource "google_dns_record_set" "ghe" {
  name         = "ghe.test.secops.gcp.getunblocked.com."
  managed_zone = google_dns_managed_zone.secops.name
  type         = "A"
  ttl          = 300
  rrdatas = [google_compute_address.ghe_ip.address]
}