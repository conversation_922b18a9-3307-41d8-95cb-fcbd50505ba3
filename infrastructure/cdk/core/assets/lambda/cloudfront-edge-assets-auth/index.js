'use strict';
/*
 - This function is called on Origin Request events.
 - It's responsible for validating auth token provided by user
   and cross-checking teamId specified in path against token claims
 */

// auth-config.json provides pub keys for jwt verification
const authConfig = require('./auth-config.json');
const jwt = require('jsonwebtoken');
const querystring = require('querystring');
const lambdaName = 'assets-auth';
const CF_ENDPOINT_PREFIX = '/assets';

const parseCookies = (headers) => {
    // Cookies are present in the HTTP header "Cookie" that may be present multiple times.
    // This utility function parses occurrences  of that header and splits out all the cookies and their values
    // A simple object is returned that allows easy access by cookie name: e.g. cookies["nonce"]
    if (!headers['cookie'] || !headers['cookie'][0]) {
        return {};
    }

    return headers['cookie'][0].value
        .split(';')
        .map((v) => v.split('='))
        .reduce((acc, v) => {
            acc[decodeURIComponent(v[0].trim())] = decodeURIComponent(v[1].trim());
            return acc;
        }, {});
};

exports.handler = (event, context, callback) => {
    const request = event.Records[0].cf.request;
    const cookies = parseCookies(request.headers);
    const headers = request.headers;
    const sanitizedUri = request.uri.replace(/(\/)\/+/g, '$1');
    const host = headers['host'][0].value;
    const params = querystring.parse(request.querystring);

    if (headers['access-control-request-headers']) {
        console.log('Handling CORS preflight request: ' + JSON.stringify(headers));
        let response = createCorsPreflightResponse();
        callback(null, response);
        return;
    }

    let authTokenProvider = '';
    if (typeof params.authorization != 'undefined') {
        authTokenProvider = params.authorization;
        delete request.headers.authorization;
    } else if (typeof headers.authorization != 'undefined') {
        authTokenProvider = headers.authorization[0].value;
    } else if (typeof cookies.authorization != 'undefined') {
        authTokenProvider = cookies.authorization;
    } else {
        // This should be replaced with a redirect to an error asset
        // We would assume users are trying to fetch asses without a valid
        // Unblocked client
        // TODO: replace with a redirect to error asset
        let req = customErrorImage(event, 401);
        callback(null, req);
        return;

        /*let response = createErrorResponse(401, 'Unauthorized: Invalid token', request, lambdaName);
        callback(null, response);
        return;*/
    }
    const authToken = authTokenProvider.replace('Bearer', '').trim();

    // Step 1: Get auth pub key from config file
    // Step 2: Verify JWT issuer and audience values
    // Step 3: Get team_id from claims, check if uri is asking for correct team ID
    // If all good, allow request to get through
    if (authConfig[host]) {
        if (authConfig[host]['public_key']) {
            try {
                let pubKeyb64 = new Buffer.from(authConfig[host]['public_key'], 'base64');
                let pubKeyDecoded = pubKeyb64.toString('ascii');

                // Actual token and team ID validation
                try {
                    const decodedToken = jwt.verify(authToken, pubKeyDecoded, {
                        audience: 'com.unblocked.token',
                        issuer: authConfig[host]['token_issuer'],
                        algorithms: ['RS256'],
                    });

                    // Expected pattern is "/assets/{teamID}/{assetID}"
                    // Filter out empty path separators
                    let pathComponents = sanitizedUri.split('/').filter(Boolean);
                    if (pathComponents.length !== 3) {
                        let response = createErrorResponse(
                            400,
                            `invalid request path ${request.uri} - ${sanitizedUri}`,
                            request,
                            lambdaName
                        );
                        callback(null, response);
                        return;
                    }

                    if (isUUID(pathComponents[1]) && decodedToken.teams.includes(pathComponents[1])) {
                        // Auth success, allow request to proceed
                        // To avoid having requests cached with authorization token as part of their cache key,
                        // we are using query strings which are not cached to pass along authorization info
                        // to origin request function.
                        const requestQuery = {
                            api: host,
                            authorization: `Bearer ${authToken}`,
                        };
                        if (params.unblockedProductAgent) {
                            requestQuery['unblockedProductAgent'] = params.unblockedProductAgent;
                        }
                        request['querystring'] = querystring.stringify(requestQuery);

                        if (sanitizedUri.startsWith(CF_ENDPOINT_PREFIX)) {
                            request.uri = sanitizedUri.slice(CF_ENDPOINT_PREFIX.length);
                        }

                        request.headers = createCorsResponseHeaders(request.headers);
                        console.log(`Authorization succeeded: ${JSON.stringify(request)}`);

                        callback(null, request);
                        return;
                    }

                    throw `invalid team id ${pathComponents[2]}`;
                } catch (err) {
                    let response = createErrorResponse(401, `${err}`, request, lambdaName);
                    callback(null, response);
                    return;
                }
            } catch (err) {
                console.log(`failed to decode public key for ${host} due to ${err}`);
            }
        } else {
            console.log(`failed to load public key for host: ${host}`);
        }
    } else {
        console.log(`failed to load auth config for host: ${host}`);
    }

    let response = createErrorResponse(
        500,
        'the server encountered an unexpected condition that prevented it from fulfilling the request',
        request,
        lambdaName
    );
    callback(null, response);
};

function createCorsResponseHeaders(headers) {
    headers = headers || {};
    headers['access-control-allow-origin'] = [{ key: 'Access-Control-Allow-Origin', value: '*' }];
    headers['access-control-allow-headers'] = [{ key: 'Access-Control-Allow-Headers', value: '*' }];
    return headers;
}

function createCorsPreflightResponse() {
    return {
        status: '200',
        headers: createCorsResponseHeaders({}),
        statusDescription: 'CORS preflight response',
    };
}

function createErrorResponse(code, description, request, lambdaName) {
    const requestStr = JSON.stringify(request);
    // Debug headers
    const responseHeaders = createCorsResponseHeaders({});
    if (code >= 500) {
        responseHeaders['x-lambda-request-uri'] = [{ value: request.uri }];
        responseHeaders['x-lambda-name'] = [{ value: lambdaName }];
    }

    console.log(
        `Returning an error response: ${JSON.stringify(
            responseHeaders
        )} to request : ${requestStr} ${code}  ${description}`
    );

    return {
        status: code.toString(),
        headers: responseHeaders,
        statusDescription: description,
    };
}

function isUUID(uuid) {
    let s = '' + uuid;

    s = s.match('^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$');
    if (s === null) {
        return false;
    }
    return true;
}

function customErrorImage(event, errorCode) {
    let request = event.Records[0].cf.request;
    const requestStr = JSON.stringify(request);
    console.log(`Request caused error code: ${errorCode} for request : ${requestStr}`);

    request.uri = `/error-assets/error-${errorCode}.png`;
    request['querystring'] = '';

    if (request.headers.authorization) delete request.headers.authorization;

    return request;
}
