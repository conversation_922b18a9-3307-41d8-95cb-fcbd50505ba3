variable "project_id" {
  description = "The GCP project ID"
  type        = string
  default     = "secops-467621"
}

variable "region" {
  description = "GCP region"
  type        = string
  default     = "us-west1"
}

variable "google_bucket_location" {
  description = "GCP bucket location"
  type        = string
  default     = "US"
}

variable "vpc_name" {
  description = "VPC name"
  type        = string
  default     = "core-vpc"
}

variable "public_subnet_cidr" {
  description = "Public subnet CIDR"
  type        = string
  default     = "**********/20"
}

variable "private_subnet_cidr" {
  description = "Private subnet CIDR"
  type        = string
  default     = "***********/20"
}

variable "gke_pods_cidr" {
  description = "value of the secondary range for pods"
  type        = string
  default     = "***********/20"
}

variable "gke_services_cidr" {
  description = "value of the secondary range for services"
  type        = string
  default     = "***********/20"
}

variable "gke_name" {
  description = "Name of the GKE Cluster"
  type        = string
  default     = "secops"
}

variable "master_authorized_networks" {
  description = "List of master authorized networks for GKE control plane access"
  type = list(object({
    cidr_block   = string
    display_name = string
  }))
  default = [
    {
      cidr_block   = "**********/16"
      display_name = "core-vpc cidr"
    }
  ]
}

variable "dns_name" {
  description = "DNS name"
  type        = string
  default     = "secops.gcp.getunblocked.com"
}

variable "vpn_ssh_access" {
  description = "CIDR for SSH access to the VPN VM"
  type        = string
  default     = "**************/32"
}

variable "gke_gateway_name" {
  description = "Name of the gateway for GKE"
  type        = string
  default     = "gke-secops-edge-global"
}

variable "gke_test_certmap_name" {
  description = "Certificate maps name for the testing servers"
  type        = string
  default     = "gke-secops-test"
}

variable "test_wildcard_domain" {
  description = "Wildcard domain for the testing servers"
  type        = list(any)
  default = [
    "*.test.secops.gcp.getunblocked.com",
  ]
}

variable "test_hostnames" {
  description = "Hostnames for the testing servers"
  type        = list(any)
  default = [
    "bitbucket.test.secops.gcp.getunblocked.com",
    "jira.test.secops.gcp.getunblocked.com",
    "confluence.test.secops.gcp.getunblocked.com",
    "jenkins.test.secops.gcp.getunblocked.com",
    "gitlab.test.secops.gcp.getunblocked.com",
  ]
}
