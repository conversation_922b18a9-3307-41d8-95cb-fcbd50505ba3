import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as targets from 'aws-cdk-lib/aws-route53-targets';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import {
    OriginAccessIdentity,
    AddBehaviorOptions,
    CacheHeaderBehavior,
    EdgeLambda,
    HeadersFrameOption,
    HeadersReferrerPolicy,
    IResponseHeadersPolicy,
    OriginRequestCookieBehavior,
    OriginRequestHeaderBehavior,
    OriginRequestPolicy,
    OriginRequestQueryStringBehavior,
    ResponseHeadersPolicy,
    IOrigin,
} from 'aws-cdk-lib/aws-cloudfront';
import { AcmStack } from '../acm/acm-stack';
import * as origin from 'aws-cdk-lib/aws-cloudfront-origins';
import { Dns } from '../common/dns/config';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as path from 'path';
import { WebAclCloudFrontStack } from '../wafv2/web-acl-cloudfront-stack';
import * as iam from 'aws-cdk-lib/aws-iam';
import { CloudFront } from '../common/cloudfront/config';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as s3 from 'aws-cdk-lib/aws-s3';

interface CloudFrontStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    cloudFront: CloudFront;
}

export interface CloudFrontStackProps extends StackProps {
    buildConfig: CloudFrontStackConfig;
    acmStack: AcmStack;
    webAclCloudFrontStack: WebAclCloudFrontStack;
}

export class CloudFrontStack extends Stack {
    readonly distribution: cloudfront.Distribution;
    readonly originAccessIdentity: cloudfront.OriginAccessIdentity;
    readonly originAccessIdentityIamPolicy: iam.PolicyStatement;
    private readonly securityResponseHeadersPolicy: IResponseHeadersPolicy;

    constructor(scope: Construct, id: string, props: CloudFrontStackProps) {
        super(scope, id, props);

        const cloudFrontConf = props.buildConfig.cloudFront;
        /**
         *  ACM Cert Setup
         */
        const acmCertificate = props.acmStack.certs.get(cloudFrontConf.certName);
        if (acmCertificate == undefined) {
            throw new Error(`failed to retrieve ${props.buildConfig.dns.route53HostedZoneName} cert from acm stack`);
        }

        const allViewerOriginRequestPolicy = new OriginRequestPolicy(this, 'AllViewerWithCloudFrontHeaders', {
            originRequestPolicyName: 'AllViewerWithCloudFrontHeaders',
            cookieBehavior: OriginRequestCookieBehavior.all(),
            headerBehavior: OriginRequestHeaderBehavior.all('CloudFront-Viewer-Address'),
            queryStringBehavior: OriginRequestQueryStringBehavior.all(),
        });

        const csp = cloudFrontConf.csp.join('; ');

        this.securityResponseHeadersPolicy = new ResponseHeadersPolicy(this, 'SecurityResponseHeaderPolicy', {
            responseHeadersPolicyName: 'SecurityResponseHeaderPolicy',
            comment: 'Default security headers. Gives us an "A" grade on securityheaders.com',
            securityHeadersBehavior: {
                strictTransportSecurity: {
                    accessControlMaxAge: Duration.seconds(15724800),
                    includeSubdomains: true,
                    override: true,
                },
                frameOptions: {
                    frameOption: HeadersFrameOption.SAMEORIGIN,
                    override: true,
                },
                referrerPolicy: {
                    referrerPolicy: HeadersReferrerPolicy.STRICT_ORIGIN,
                    override: true,
                },
                contentTypeOptions: {
                    override: true,
                },
                contentSecurityPolicy: {
                    contentSecurityPolicy: csp,
                    override: true,
                },
            },
            customHeadersBehavior: {
                customHeaders: [
                    {
                        header: 'Permissions-Policy',
                        value: 'fullscreen=(self), camera=(), microphone=(), display-capture=(), geolocation=()',
                        override: true,
                    },
                ],
            },
            removeHeaders: ['Server'],
        });

        // Create Origin Access Identity to be use Canonical User Id in S3 bucket policy
        this.originAccessIdentity = new OriginAccessIdentity(this, 'OAI', {
            comment: 'created_by_cdk',
        });
        this.originAccessIdentityIamPolicy = new iam.PolicyStatement();
        this.originAccessIdentityIamPolicy.addActions('s3:GetBucket*');
        this.originAccessIdentityIamPolicy.addActions('s3:GetObject*');
        this.originAccessIdentityIamPolicy.addActions('s3:List*');
        this.originAccessIdentityIamPolicy.addCanonicalUserPrincipal(
            this.originAccessIdentity.cloudFrontOriginAccessIdentityS3CanonicalUserId
        );

        // Adding landing page as domain root
        const rootOrigin = this.getS3BucketOrigin('/static-site-stack/landing-page', 'rootOriginBucket');
        this.distribution = new cloudfront.Distribution(this, `MainCloudFrontDistro`, {
            defaultBehavior: {
                origin: rootOrigin,
                originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
                cachePolicy: new cloudfront.CachePolicy(this, `cachePolicyForRoot`, {
                    enableAcceptEncodingGzip: true,
                    enableAcceptEncodingBrotli: true,
                }),
                compress: true,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                responseHeadersPolicy: this.securityResponseHeadersPolicy,
            },
            defaultRootObject: 'index.html',
            domainNames: cloudFrontConf.domainNames,
            priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,
            certificate: acmCertificate,
            webAclId: props.webAclCloudFrontStack.webAclCloudFrontArn,
            httpVersion: cloudfront.HttpVersion.HTTP2,
            minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
            errorResponses: [
                {
                    httpStatus: 503,
                    responseHttpStatus: 503,
                    responsePagePath: '/503.html',
                    ttl: Duration.minutes(1),
                },
            ],
        });

        new route53.ARecord(this, `SiteAliasRecord-${props.buildConfig.dns.route53HostedZoneName}`, {
            recordName: props.buildConfig.dns.route53HostedZoneName,
            target: route53.RecordTarget.fromAlias(new targets.CloudFrontTarget(this.distribution)),
            zone: props.acmStack.envRootHostedZone,
        });

        this.distribution.addBehavior('/blog*', new origin.HttpOrigin('blog.getunblocked.com'), {
            originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
            compress: true,
            cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
            allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
            viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
            responseHeadersPolicy: this.securityResponseHeadersPolicy,
            edgeLambdas: this.generateBlogSiteEdgeLambdas('/blog/*'),
            functionAssociations: this.generateLandingSiteCloudfrontFunctions('/blog/*'),
        });

        // Get S3 origin for Dashboard bucket
        const dashboardBucketS3Origin = this.getS3BucketOrigin('/static-site-stack/dashboard', 'dashboardBucket');

        this.distribution.addBehavior(
            '/dashboard*',
            dashboardBucketS3Origin,
            this.generateStaticSiteBehavior('/dashboard/*')
        );

        // Get S3 origin for Dashboard bucket
        const downloadAssetsBucketS3Origin = this.getS3BucketOrigin(
            '/static-site-stack/download-assets',
            'downloadAssetsBucket'
        );
        // TODO: Will be removed once we have finished cutting over to /releases in app stack
        this.distribution.addBehavior(
            '/download-assets/*',
            downloadAssetsBucketS3Origin,
            this.generateStaticSiteBehavior('/download-assets/*')
        );

        // Get S3 origin for Releases bucket
        const releasesBucketS3Origin = this.getS3BucketOrigin('/static-site-stack/releases', 'releasesBucket');

        // Add releases origin
        this.distribution.addBehavior(
            '/releases/*',
            releasesBucketS3Origin,
            this.generateStaticSiteBehavior('/releases/*')
        );

        // S3 role allowing Lambda@edge to generate signed S3 urls for streaming assets
        const lambdaEdgeLookupStreamingAssets = new iam.Role(this, 'lambda-edge-lookup-access', {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by lambda edge function to generate signed download urls for CF caching',
            inlinePolicies: {
                DescribeACMCerts: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: ['arn:aws:s3:::agora-streaming-assets-*'],
                            actions: ['s3:GetObject'],
                        }),
                        new iam.PolicyStatement({
                            resources: [`arn:aws:kms:*:${props.buildConfig.awsEnvAccount.awsAccountID}:key/*`],
                            actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                        }),
                    ],
                }),
            },
            managedPolicies: [
                //iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSLambdaVPCAccessExecutionRole"),
                iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
            ],
        });

        // Cloudfront endpoint to handle all customer asset requests
        this.distribution.addBehavior(
            '/assets/*',
            new origin.HttpOrigin('static-error-assets-dev-us-east-1.s3.amazonaws.com'),
            {
                originRequestPolicy: allViewerOriginRequestPolicy,
                compress: true,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
                cachePolicy: new cloudfront.CachePolicy(this, `CachePolicyForAssets`, {
                    cachePolicyName: 'CachePolicyForAssets',
                    enableAcceptEncodingGzip: true,
                    enableAcceptEncodingBrotli: true,
                    minTtl: Duration.seconds(0),
                    headerBehavior: CacheHeaderBehavior.allowList(
                        'Origin',
                        'Access-Control-Request-Method',
                        'Access-Control-Request-Headers',
                        'Range',
                        'X-Unblocked-Product-Agent'
                    ),
                }),
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                edgeLambdas: [
                    {
                        // Lambda@Edge to validate auth token
                        functionVersion: new cloudfront.experimental.EdgeFunction(this, `authTokenValidation-Assets`, {
                            runtime: lambda.Runtime.NODEJS_18_X,
                            handler: 'index.handler',
                            code: lambda.Code.fromAsset(
                                path.join(__dirname, '../../assets/lambda/cloudfront-edge-assets-auth')
                            ),
                        }),
                        eventType: cloudfront.LambdaEdgeEventType.VIEWER_REQUEST,
                    },
                    {
                        // Lambda@Edge perform asset metadata lookup and identify correct origin bucket
                        functionVersion: new cloudfront.experimental.EdgeFunction(this, `originBucketLookup-Assets`, {
                            runtime: lambda.Runtime.NODEJS_18_X,
                            handler: 'index.handler',
                            code: lambda.Code.fromAsset(
                                path.join(__dirname, '../../assets/lambda/cloudfront-edge-assets-origin-lookup')
                            ),
                            role: lambdaEdgeLookupStreamingAssets,
                        }),
                        eventType: cloudfront.LambdaEdgeEventType.ORIGIN_REQUEST,
                    },
                ],
                responseHeadersPolicy: this.securityResponseHeadersPolicy,
            }
        );

        // Adding ALB origin for API services
        this.distribution.addBehavior(
            '/api/*',
            new origin.HttpOrigin(`alb.${props.buildConfig.dns.route53HostedZoneName}`),
            {
                originRequestPolicy: allViewerOriginRequestPolicy,
                compress: false,
                cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
                allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
                viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                responseHeadersPolicy: this.securityResponseHeadersPolicy,
            }
        );
        // WARNING: This global wildcard match must be the last entry here to ensure proper ordering of behaviours.
        // Cloudfront determines precedence based off ordering of behaviours.
        // https://docs.aws.amazon.com/cloudfront/latest/APIReference/API_CacheBehavior.html
        this.distribution.addBehavior('/*', rootOrigin, this.generateStaticSiteBehavior('LandingRoot'));
    }

    // Add each non-root static site bucket as a behavior to CloudFront
    private generateStaticSiteBehavior(key: string): AddBehaviorOptions {
        // This is a hack while we figure out new landing page stuff
        let cfFunctions = undefined;
        let edgeLambdas = undefined;
        if (key.startsWith('LandingRoot')) {
            edgeLambdas = this.generateLandingSiteEdgeLambdas(key);
            cfFunctions = this.generateLandingSiteCloudfrontFunctions(key);
        } else {
            edgeLambdas = this.generateStaticSiteEdgeLambdas(key);
        }

        return {
            originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
            compress: true,
            allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
            cachePolicy: new cloudfront.CachePolicy(this, `cachePolicyFor${key}`, {
                headerBehavior: cloudfront.CacheHeaderBehavior.allowList('X-CustomHeader'),
                enableAcceptEncodingGzip: true,
                enableAcceptEncodingBrotli: true,
            }),
            viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
            edgeLambdas: edgeLambdas,
            functionAssociations: cfFunctions,
            responseHeadersPolicy: this.securityResponseHeadersPolicy,
        };
    }

    private generateStaticSiteEdgeLambdas(key: string): EdgeLambda[] {
        return [
            {
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `WwwRedirect${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-www-redirect')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.VIEWER_REQUEST,
            },
            {
                // When using S3 origins with a path e.g '/dashboard', by default it translates to a subdir on S3
                // we need to rewrite the path to remove first level path components before sending requests to S3 origin.
                // S3 does not serve index.html in sub directory so we have to keep things at bucket root
                // https://stackoverflow.com/a/63777697
                // https://stackoverflow.com/a/31574851
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `EdgePathRewrite${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-path-rewrite')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_REQUEST,
            },
            {
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `EdgeCustomError${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-custom-error')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_RESPONSE,
            },
        ];
    }

    private generateBlogSiteEdgeLambdas(key: string): EdgeLambda[] {
        return [
            {
                // Reverse proxy function
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `EdgePathRewrite${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-blog-path-rewrite')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_REQUEST,
            },
            {
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `EdgeCustomError${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-custom-error')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_RESPONSE,
            },
        ];
    }

    private generateLandingSiteCloudfrontFunctions(key: string): cloudfront.FunctionAssociation[] {
        return [
            {
                function: new cloudfront.Function(this, `CF-Func-CspNonceViewerResponse${key}`, {
                    code: cloudfront.FunctionCode.fromFile({
                        filePath: path.join(
                            __dirname,
                            '../../assets/lambda/cloudfront-function-csp-nonce-viewer-response/index.js'
                        ),
                    }),
                    runtime: cloudfront.FunctionRuntime.JS_2_0,
                }),
                eventType: cloudfront.FunctionEventType.VIEWER_RESPONSE,
            },
            {
                function: new cloudfront.Function(this, `CF-Func-UrlRedirect${key}`, {
                    code: cloudfront.FunctionCode.fromFile({
                        filePath: path.join(
                            __dirname,
                            '../../assets/lambda/cloudfront-function-edge-redirect/index.js'
                        ),
                    }),
                    runtime: cloudfront.FunctionRuntime.JS_2_0,
                }),
                eventType: cloudfront.FunctionEventType.VIEWER_REQUEST,
            },
        ];
    }

    private generateLandingSiteEdgeLambdas(key: string): EdgeLambda[] {
        // S3 role allowing Lambda@edge to access S3
        const lambdaEdgeObjectGet = new iam.Role(this, 'lambda-edge-landing-object-access', {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by lambda edge function to retrieve objects from S3',
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
            inlinePolicies: {
                DescribeACMCerts: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: ['arn:aws:s3:::landing-page.*'],
                            actions: ['s3:GetObject'],
                        }),
                        new iam.PolicyStatement({
                            resources: ['arn:aws:kms:us-east-1::key/*'],
                            actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                        }),
                    ],
                }),
            },
        });
        return [
            {
                // When using S3 origins with a path e.g '/dashboard', by default it translates to a subdir on S3
                // we need to rewrite the path to remove first level path components before sending requests to S3 origin.
                // S3 does not serve index.html in sub directory so we have to keep things at bucket root
                // https://stackoverflow.com/a/63777697
                // https://stackoverflow.com/a/31574851
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `NewLanding-NewEdgePathRewrite${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-edge-path-rewrite')
                    ),
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_REQUEST,
            },
            {
                functionVersion: new cloudfront.experimental.EdgeFunction(this, `NewLanding-NewEdgeCustomError${key}`, {
                    runtime: lambda.Runtime.NODEJS_18_X,
                    handler: 'index.handler',
                    code: lambda.Code.fromAsset(
                        path.join(__dirname, '../../assets/lambda/cloudfront-landing-page-origin-response')
                    ),
                    role: lambdaEdgeObjectGet,
                }),
                eventType: cloudfront.LambdaEdgeEventType.ORIGIN_RESPONSE,
            },
        ];
    }

    private getS3BucketOrigin(ssmParamName: string, resourceName: string): IOrigin {
        let arn = ssm.StringParameter.valueFromLookup(this, ssmParamName);
        // Temp hack for a CDK bug https://sdhuang32.github.io/ssm-StringParameter-valueFromLookup-use-cases-and-internal-synth-flow/
        if (arn.includes('dummy-value')) {
            arn = 'arn:aws:s3:::dummy-value';
        }
        const bucket = Bucket.fromBucketArn(this, resourceName, arn);
        const bucketPolicy = new s3.BucketPolicy(this, `cf-oai-access-policy-${resourceName}`, {
            bucket: bucket,
        });

        // Create bucket policy to grant CloudFront OAI access to content
        const originAccessIdentityIamPolicy = new iam.PolicyStatement();
        originAccessIdentityIamPolicy.addActions('s3:GetBucket*');
        originAccessIdentityIamPolicy.addActions('s3:GetObject*');
        originAccessIdentityIamPolicy.addActions('s3:List*');
        originAccessIdentityIamPolicy.addResources(bucket.bucketArn);
        originAccessIdentityIamPolicy.addResources(`${bucket.bucketArn}/*`);
        originAccessIdentityIamPolicy.addCanonicalUserPrincipal(
            this.originAccessIdentity.cloudFrontOriginAccessIdentityS3CanonicalUserId
        );

        bucketPolicy.document.addStatements(originAccessIdentityIamPolicy);
        //bucket.addToResourcePolicy(originAccessIdentityIamPolicy)

        let roleArn = ssm.StringParameter.valueFromLookup(this, `${ssmParamName}-deployer-role`);
        if (roleArn.includes('dummy-value')) {
            roleArn = 'arn:aws:iam::129540529571:role/dummy';
        }
        const deployerRole = iam.Role.fromRoleArn(this, `${ssmParamName}ImportedDeployerRole`, roleArn, {
            mutable: false,
        });
        bucketPolicy.document.addStatements(
            new iam.PolicyStatement({
                actions: ['s3:PutObject', 's3:GetObject', 's3:DeleteObject', 's3:PutObjectAcl'],
                resources: [bucket.bucketArn, `${bucket.bucketArn}/*`],
                principals: [deployerRole],
            })
        );

        // Bucket policies
        bucketPolicy.document.addStatements(
            new iam.PolicyStatement({
                actions: ['s3:ListBucket'],
                resources: [bucket.bucketArn, `${bucket.bucketArn}/*`],
                principals: [deployerRole],
            })
        );

        return origin.S3BucketOrigin.withOriginAccessIdentity(bucket, {
            originAccessIdentity: this.originAccessIdentity,
        });
    }
}
