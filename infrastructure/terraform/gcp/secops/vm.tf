locals {
  vpn_vm_name        = "secops-vpn"
  vpn_machine_type   = "e2-micro"
  vpn_ssh_access     = var.vpn_ssh_access
  vpn_image          = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
  vpn_boot_disk_size = 20

  ghe_vm_name        = "secops-ghe"
  ghe_machine_type   = "e2-highmem-4"
  ghe_ssh_access     = "75.157.198.225/32"
  ghe_image          = "projects/github-enterprise-public/global/images/github-enterprise-3-14-16"
  ghe_boot_disk_size = 201
  ghe_data_disk_size = 150
}

# VPN VM
resource "google_compute_instance" "vpn" {
  name         = local.vpn_vm_name
  machine_type = local.vpn_machine_type
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = local.vpn_image
      size  = local.vpn_boot_disk_size
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = module.vpc.network_name
    subnetwork = local.public_subnet_name

    # Add external IP
    access_config {
      nat_ip = google_compute_address.vpn_ip.address
    }
  }

  metadata_startup_script = <<-EOT
    #!/bin/bash
    apt-get update -y
    apt-get install -y curl
  EOT

  tags = ["${local.vpn_vm_name}"]
}

# temp VM
resource "google_compute_instance" "temp" {
  name         = "temp"
  machine_type = "e2-small"
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = local.vpn_image
      size  = 100
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = module.vpc.network_name
    subnetwork = local.public_subnet_name

    # Add external IP
    access_config {
      nat_ip = google_compute_address.temp_ip.address
    }
  }

  metadata_startup_script = <<-EOT
    #!/bin/bash
    apt-get update -y
    apt-get install -y curl
  EOT

  tags = ["temp"]
}

# GHE VM
resource "google_compute_instance" "ghe" {
  name         = local.ghe_vm_name
  machine_type = local.ghe_machine_type
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = local.ghe_image
      size  = local.ghe_boot_disk_size
      type  = "pd-standard"
    }
  }

  # Attach the SECOND disk here
  attached_disk {
    source      = google_compute_disk.ghe_data.id
    device_name = "ghe-data"
    mode        = "READ_WRITE"
  }

  network_interface {
    network    = module.vpc.network_name
    subnetwork = local.public_subnet_name

    # Add external IP
    access_config {
      nat_ip = google_compute_address.ghe_ip.address
    }
  }

  # Enable serial console (helpful for troubleshooting per GHES docs)
  metadata = {
    serial-port-enable = "1"
  }

  tags = ["${local.ghe_vm_name}"]

  depends_on = [google_compute_disk.ghe_data]
}

# Data disk for GHES app data (>=150 GB for small installs; size to your user tier)
resource "google_compute_disk" "ghe_data" {
  name = "${local.ghe_vm_name}-data"
  type = "pd-standard"
  size = local.ghe_data_disk_size
  zone = "${var.region}-a"
}