{"Records": [{"cf": {"config": {"distributionDomainName": "d111111abcdef8.cloudfront.net", "distributionId": "EDFDVBD6EXAMPLE", "eventType": "viewer-request", "requestId": "4TyzHTaYWb1GX1qTfsHhEqV6HUDd_BzoBZnwfnvQc_1oF26ClkoUSEQ=="}, "request": {"clientIp": "**************", "headers": {"host": [{"key": "host", "value": "dev.getunblocked.com"}], "x-forwarded-for": [{"key": "X-Forwarded-For", "value": "**************"}], "user-agent": [{"key": "User-Agent", "value": "Amazon CloudFront"}], "via": [{"key": "Via", "value": "1.1 2fce4d7a8040ab4194bdbe927610b6c2.cloudfront.net (CloudFront)"}], "authorization": [{"key": "Authorization", "value": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}]}, "method": "GET", "querystring": "", "uri": "/assets/c9c47779-235f-4ca9-aefb-96758fa96650/buddybuild.png"}}}]}