from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, File, UploadFile
from docling.document_converter import DocumentConverter, DocumentStream
from markitdown import MarkItDown
from io import BytesIO
import uvicorn
import os
import time
import base64
import httpx  # Use httpx for async requests
from typing import I<PERSON>, Dict, BinaryIO
from document_processor.llm_constants import DEFAULT_LLM_ENDPOINT_URL

# Global variables for reusable objects
document_converter = DocumentConverter()
markitdown = MarkItDown()

app = FastAPI()

DOCLING_EXTENSIONS = {
    ".pdf",
    ".docx",
    ".xlsx",
    ".html",
    ".htm",
    ".md",
}

MARKITDOWN_EXTENSIONS = {
    ".csv",
}

IMAGE_EXTENSIONS = {
    ".png",
    ".jpg",
    ".jpeg",
}


@app.post("/")
@app.post("/invocations")
async def convert(file: UploadFile = File(...)) -> str:
    try:
        filename, ext = os.path.splitext(file.filename)
        ext = ext.lower()

        with file.file as stream:
            if ext in DOCLING_EXTENSIONS:
                result = convert_document_docling(
                    stream=stream, filename=file.filename, ext=ext
                )
            elif ext in MARKITDOWN_EXTENSIONS:
                result = convert_document_markitdown(
                    stream=stream, filename=file.filename, ext=ext
                )
            elif ext in IMAGE_EXTENSIONS:
                result = await convert_image(
                    stream=stream, filename=file.filename, ext=ext
                )  # Await the async function
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported file extension: {ext}. Supported extensions are: "
                    f"{', '.join(DOCUMENT_EXTENSIONS | IMAGE_EXTENSIONS)}",
                )

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def convert_document_markitdown(stream: BinaryIO, filename: str, ext: str) -> str:
    content = stream.read()
    bytes_stream = BytesIO(content)
    result = markitdown.convert_stream(stream=bytes_stream, file_extension=ext)
    return result.text_content


async def convert_image(stream: BinaryIO, filename: str, ext: str) -> str:
    image_bytes = stream.read()
    ocr_text = convert_document_docling(BytesIO(image_bytes), filename, ext)
    ml_description = await convert_document_llama(
        BytesIO(image_bytes), filename, ext
    )  # Await the async function
    return f"# {filename} \n ## Description \n {ml_description} \n ## Keywords \n {ocr_text}"


def convert_document_docling(stream: BinaryIO, filename: str, ext) -> str:
    content = stream.read()
    bytes_stream = BytesIO(content)
    source = DocumentStream(name=filename, stream=bytes_stream)
    result = document_converter.convert(source)
    return result.document.export_to_markdown()


async def convert_document_llama(stream: IO[bytes], filename: str, ext: str) -> str:
    image_bytes = stream.read()
    encoded_image = base64.b64encode(image_bytes).decode("utf-8")
    mime_type = f"image/{ext.lstrip('.').lower().replace('jpg', 'jpeg')}"
    data_url = f"data:{mime_type};base64,{encoded_image}"

    payload = {
        "model": "tgi",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"Describe the {filename} image using Markdown for embedding in a vector store.",
                    },
                    {"type": "image_url", "image_url": {"url": data_url}},
                ],
            }
        ],
        "stream": False,
        "max_tokens": 500,
    }

    headers = {"Content-Type": "application/json"}
    timeout = httpx.Timeout(10.0, read=35.0)
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            response = await client.post(
                DEFAULT_LLM_ENDPOINT_URL, json=payload, headers=headers
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"LLM API request failed: {str(e)}"
            )


@app.get("/ping")
async def ping() -> dict[str, str]:
    return {"message": "ok"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
