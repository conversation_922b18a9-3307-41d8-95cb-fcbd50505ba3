namespaceOverride: "default"

controllerServiceAccount:
  namespace: default
  name: arc-gha-rs-controller

githubConfigUrl: "https://github.com/NextChapterSoftware/unblocked"   # or org URL
githubConfigSecret: gha-pat

runnerScaleSetName: "gcp-arc-runner-set"

minRunners: 0
maxRunners: 20

labels:
  workload: gha

# Listener pod template – MUST include a container named "listener"
listenerTemplate:
  spec:
    containers:
      - name: listener        # don't change this name
        # (you usually don't need to set image/args; ARC fills them in)
    nodeSelector:
      secops-node-pool-1: "true"

containerMode:
  type: "dind"

# Runner pod template – include the runner container if you touch spec
template:
  spec:
    tolerations:
      - key: "github-actions-runner"
        operator: "Equal"
        value: "9x"
        effect: "NoSchedule"
    nodeSelector:
      workload: github-actions-runner
