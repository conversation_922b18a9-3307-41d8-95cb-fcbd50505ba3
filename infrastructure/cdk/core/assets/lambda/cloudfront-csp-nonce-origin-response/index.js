/*
 * This Lambda edge function is used to set CSP nonce
 * header and modify HTTP script tags with the randomly
 * generated value in HTML response for every origin response
 * relating to either site root "/" or any file ending with ".html"
 */
const config = require('./config.json');
const path = require('path');
const fs = require('fs');
const { GetObjectCommand, S3Client } = require('@aws-sdk/client-s3');

exports.handler = async (event) => {
    const request = event.Records[0].cf.request;
    const response = event.Records[0].cf.response;
    const host = request.headers['host'][0].value;
    const uri = request.uri;

    const bucket = config[host]['bucket'];
    const region = config[host]['region'];
    const cacheTtl = config[host]['cache_ttl_seconds'];

    // Check if the request path is root directory or ends with .html
    if (uri === '/' || uri.endsWith('.html')) {
        // Generate a random nonce
        const nonce = generateRandomNonce();

        const streamToString = (stream) =>
            new Promise((resolve, reject) => {
                const chunks = [];
                stream.on('data', (chunk) => chunks.push(chunk));
                stream.on('error', reject);
                stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
            });

        try {
            let objectContent = '';
            if (host.includes('localhost')) {
                // Read html test file
                objectContent = fs.readFileSync('./test.html', 'utf-8');
            } else {
                const s3Client = new S3Client({
                    region: region,
                });

                const getObjectCommand = new GetObjectCommand({
                    Bucket: bucket,
                    Key: uri.substring(1),
                });

                const objectResponse = await s3Client.send(getObjectCommand);
                objectContent = await objectResponse.Body.transformToString('utf-8');
            }

            // Apply nonce changes to the HTML content
            const modifiedHtml = addNoncesToHtml(objectContent, nonce);
            const modifiedHtmlString = typeof modifiedHtml === 'string' ? modifiedHtml : modifiedHtml.toString();
            // Set the modified HTML content as the response body
            response.status = '200';
            response.statusDescription = 'OK';
            response.body = modifiedHtmlString;
            response.headers['content-length'] = [{ key: 'Content-Length', value: modifiedHtml.length.toString() }];
            response.headers['content-type'] = [{ key: 'Content-Type', value: 'text/html' }];
            response.headers['x-custom-header'] = [{ key: 'X-Custom-Header', value: `${nonce}` }];

            // Set CORS headers
            response.headers['access-control-allow-origin'] = [{ key: 'Access-Control-Allow-Origin', value: '*' }];
            response.headers['access-control-allow-headers'] = [{ key: 'Access-Control-Allow-Headers', value: '*' }];

            // Set a short TTL of 10 minutes in the cache
            response.headers['cache-control'] = [{ key: 'Cache-Control', value: `max-age=${cacheTtl}` }];
        } catch (error) {
            // Handle any errors that occur during the process
            console.error('Error:', error);
            response.status = 500;
            response.statusDescription = 'Internal Server Error';
            response.body = 'An error occurred while processing the request.';
            response.headers['content-length'] = [{ key: 'Content-Length', value: response.body.length.toString() }];
            response.headers['content-type'] = [{ key: 'Content-Type', value: 'text/plain' }];
        }
    }

    return response;
};

function addNoncesToHtml(html, nonce) {
    // Add nonce attribute to script tags
    const modifiedHtmlWithNonces = html.replace(/<script[^>]*(?:nonce="[^"]*")?[^>]*>/g, (match) => {
        if (!match.includes('nonce=')) {
            return match.replace('>', ` nonce="${nonce}">`);
        } else {
            return match;
        }
    });

    return modifiedHtmlWithNonces.toString();
}

function generateRandomNonce() {
    // Generate a random nonce (you can implement your own random string generation logic here)
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const nonceLength = 16;
    let nonce = '';
    for (let i = 0; i < nonceLength; i++) {
        nonce += characters[Math.floor(Math.random() * characters.length)];
    }
    return nonce;
}
