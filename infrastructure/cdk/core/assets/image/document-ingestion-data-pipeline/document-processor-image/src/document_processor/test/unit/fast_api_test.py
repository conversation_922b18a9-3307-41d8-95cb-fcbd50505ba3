import unittest
from fastapi.testclient import TestClient
from document_processor.fast_api import app
import os

test_client = TestClient(app=app)


class TestDocumentProcessorFastAPI(unittest.TestCase):
    def setUp(self):
        # Path to the fixtures directory
        self.fixtures_dir = os.path.join(os.path.dirname(__file__), "fixtures")

    def load_fixture(self, filename):
        # Load a file from the fixtures directory
        with open(os.path.join(self.fixtures_dir, filename), "rb") as f:
            return f.read()

    def test_convert_md(self):
        file_content = self.load_fixture("example.md")
        response = test_client.post(
            "/", files={"file": ("example.md", file_content, "text/markdown")}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_csv(self):
        file_content = self.load_fixture("example.csv")
        response = test_client.post(
            "/", files={"file": ("example.csv", file_content, "application/csv")}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_pdf(self):
        file_content = self.load_fixture("example.pdf")
        response = test_client.post(
            "/", files={"file": ("example.pdf", file_content, "application/pdf")}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_docx(self):
        file_content = self.load_fixture("example.docx")
        response = test_client.post(
            "/",
            files={
                "file": (
                    "example.docx",
                    file_content,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                )
            },
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_xlsx(self):
        file_content = self.load_fixture("example.xlsx")
        response = test_client.post(
            "/",
            files={
                "file": (
                    "example.xlsx",
                    file_content,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                )
            },
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_html(self):
        file_content = self.load_fixture("example.html")
        response = test_client.post(
            "/", files={"file": ("example.html", file_content, "text/html")}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")

    def test_convert_jpg(self):
        file_content = self.load_fixture("example.jpg")
        response = test_client.post(
            "/", files={"file": ("example.jpg", file_content, "image/jpeg")}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.text.strip(), "The response should not be empty.")


if __name__ == "__main__":
    unittest.main()
