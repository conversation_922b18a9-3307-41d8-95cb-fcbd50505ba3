controller: false
runner: true

gha-runner-scale-set:
  namespaceOverride: "default"

  controllerServiceAccount:
    namespace: default
    name: arc-gha-rs-controller

  githubConfigUrl: "https://github.com/NextChapterSoftware/unblocked"   # or org URL
  githubConfigSecret: gha-pat

  runnerScaleSetName: "gcp-arc-runner-set-2x"

  minRunners: 0
  maxRunners: 20

  labels:
    workload: gha
    service: runner

  # Listener pod template – MUST include a container named "listener"
  listenerTemplate:
    spec:
      containers:
        - name: listener        # don't change this name
          # (you usually don't need to set image/args; ARC fills them in)
      nodeSelector:
        secops-node-pool-1: "true"

  containerMode:
    type: "dind"

  # Runner pod template – include the runner container if you touch spec
  template:
    metadata:
      labels:
        workload: gha
        service: runner
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: actions.github.com/scale-set-name
                    operator: In
                    values: ["gcp-arc-runner-set-2x"]
              topologyKey: kubernetes.io/hostname
      containers:
        - name: runner
          command:
          - /home/<USER>/run.sh
          image: ghcr.io/actions/actions-runner:latest
          env:
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: K8S_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          resources:
            requests: { memory: "10Gi" }
      tolerations:
        - key: "github-actions-runner"
          operator: "Equal"
          value: "2x"
          effect: "NoSchedule"
      nodeSelector:
        workload: github-actions-runner
