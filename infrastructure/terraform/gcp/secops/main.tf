locals {

  public_subnet_name  = "public-subnet"
  private_subnet_name = "private-subnet"

  node_pools = [
    {
      name                 = "secops-node-pool-1"
      machine_type         = "e2-standard-4"
      node_locations       = join(",", data.google_compute_zones.available.names)
      total_min_count      = 2
      total_max_count      = 5
      local_ssd_count      = 0
      spot                 = false
      disk_size_gb         = 20
      disk_type            = "pd-standard"
      image_type           = "COS_CONTAINERD"
      enable_gcfs          = false
      enable_gvnic         = false
      logging_variant      = "DEFAULT"
      auto_repair          = true
      auto_upgrade         = true
      service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
      preemptible          = false
      initial_node_count   = 1
      enable_private_nodes = true
    },
    {
      name                 = "gradlecache"
      machine_type         = "e2-standard-2"
      node_locations       = join(",", data.google_compute_zones.available.names)
      total_min_count      = 0
      total_max_count      = 1
      local_ssd_count      = 0
      spot                 = false
      disk_size_gb         = 100
      disk_type            = "pd-standard"
      image_type           = "COS_CONTAINERD"
      enable_gcfs          = false
      enable_gvnic         = false
      logging_variant      = "DEFAULT"
      auto_repair          = true
      auto_upgrade         = true
      service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
      preemptible          = false
      initial_node_count   = 1
      enable_private_nodes = true
    },
    {
      name                 = "github-actions-runner-9x"
      machine_type         = "n4-highcpu-32"
      node_locations       = join(",", data.google_compute_zones.available.names)
      total_min_count      = 0
      total_max_count      = 30
      local_ssd_count      = 0
      spot                 = false
      disk_size_gb         = 100
      disk_type            = "hyperdisk-balanced"
      image_type           = "COS_CONTAINERD"
      enable_gcfs          = false
      enable_gvnic         = false
      logging_variant      = "DEFAULT"
      auto_repair          = true
      auto_upgrade         = true
      service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
      preemptible          = false
      initial_node_count   = 1
      enable_private_nodes = true
    },
    {
      name                 = "github-actions-runner-2x"
      machine_type         = "n4-highcpu-8"
      node_locations       = join(",", data.google_compute_zones.available.names)
      total_min_count      = 0
      total_max_count      = 30
      local_ssd_count      = 0
      spot                 = false
      disk_size_gb         = 100
      disk_type            = "hyperdisk-balanced"
      image_type           = "COS_CONTAINERD"
      enable_gcfs          = false
      enable_gvnic         = false
      logging_variant      = "DEFAULT"
      auto_repair          = true
      auto_upgrade         = true
      service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
      preemptible          = false
      initial_node_count   = 1
      enable_private_nodes = true
    },
    {
      name                 = "github-actions-runner-4x"
      machine_type         = "n4-highcpu-16"
      node_locations       = join(",", data.google_compute_zones.available.names)
      total_min_count      = 0
      total_max_count      = 30
      local_ssd_count      = 0
      spot                 = false
      disk_size_gb         = 100
      disk_type            = "hyperdisk-balanced"
      image_type           = "COS_CONTAINERD"
      enable_gcfs          = false
      enable_gvnic         = false
      logging_variant      = "DEFAULT"
      auto_repair          = true
      auto_upgrade         = true
      service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
      preemptible          = false
      initial_node_count   = 1
      enable_private_nodes = true
    }
  ]
  node_pools_labels = {
    all = {
    }

    secops-node-pool-1 = {
      secops-node-pool-1 = true
    }

    gradlecache = {
      workload = "gradlecache"
    }

    github-actions-runner-9x = {
      workload = "github-actions-runner"
    }

    github-actions-runner-2x = {
      workload = "github-actions-runner"
    }

    github-actions-runner-4x = {
      workload = "github-actions-runner"
    }
  }

  node_pools_taints = {
    all = []

    secops-node-pool-1 = [
      {
        key    = "secops-node-pool-1"
        value  = "true"
        effect = "PREFER_NO_SCHEDULE"
      }
    ]

    gradlecache = [
      {
        key = "gradlecache"
        value = "true"
        effect = "NO_SCHEDULE"
      }
    ]

    github-actions-runner-9x = [
      {
        key = "github-actions-runner"
        value = "9x"
        effect = "NO_SCHEDULE"
      }
    ]

    github-actions-runner-2x = [
      {
        key = "github-actions-runner"
        value = "2x"
        effect = "NO_SCHEDULE"
      }
    ]

    github-actions-runner-4x = [
      {
        key = "github-actions-runner"
        value = "4x"
        effect = "NO_SCHEDULE"
      }
    ]

  }

  gateway_namespace  = "default"
  gateway_class_name = "gke-l7-global-external-managed"
  gateway_listener   = [{ name = "https", protocol = "HTTPS", port = 443 }]
  gateway_addresses  = [{ type = "NamedAddress", value = google_compute_global_address.gke_gateway.name }]

  gha_sa_name = "deploybot"

  services = [
    "cloudresourcemanager.googleapis.com"
    # add more here if you need them (e.g., "serviceusage.googleapis.com")
  ]
}

# Bucket for storing Terraform state
resource "google_storage_bucket" "terraform_state" {
  name          = "${var.project_id}_tfstate" # must be globally unique
  location      = var.google_bucket_location
  force_destroy = false

  uniform_bucket_level_access = true

  versioning {
    enabled = true
  }
}

# google_client_config and kubernetes provider must be explicitly specified like the following.
data "google_client_config" "default" {}

# Get available zones in the region
data "google_compute_zones" "available" {
  region = var.region
}

/*
module "artifact_registry" {
  source        = "GoogleCloudPlatform/artifact-registry/google"
  version       = "0.3"
  project_id    = var.project_id
  location      = var.region
  format        = "DOCKER"
  repository_id = "secops-gcp-docker-registry"
  description   = "secops docker registry"
}
*/
