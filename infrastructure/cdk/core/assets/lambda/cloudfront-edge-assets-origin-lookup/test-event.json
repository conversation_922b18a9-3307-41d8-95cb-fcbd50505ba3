{"Records": [{"cf": {"config": {"distributionDomainName": "d111111abcdef8.cloudfront.net", "distributionId": "EDFDVBD6EXAMPLE", "eventType": "origin-request", "requestId": "4TyzHTaYWb1GX1qTfsHhEqV6HUDd_BzoBZnwfnvQc_1oF26ClkoUSEQ=="}, "request": {"clientIp": "**************", "headers": {"x-forwarded-for": [{"key": "X-Forwarded-For", "value": "*************"}], "x-unblocked-product-agent": [{"key": "x-unblocked-product-agent", "value": "vscode"}], "user-agent": [{"key": "User-Agent", "value": "Amazon CloudFront"}], "via": [{"key": "Via", "value": "2.0 2afae0d44e2540f472c0635ab62c232b.cloudfront.net (CloudFront)"}], "host": [{"key": "Host", "value": "example.org"}], "cache-control": [{"key": "Cache-Control", "value": "no-cache, cf-no-cache"}], "origin": {"custom": {"customHeaders": {}, "domainName": "example.org", "keepaliveTimeout": 5, "path": "", "port": 443, "protocol": "https", "readTimeout": 30, "sslProtocols": ["TLSv1", "TLSv1.1", "TLSv1.2"]}}}, "method": "GET", "querystring": "api=localhost&authorization=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "uri": "/assets/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035"}}}]}