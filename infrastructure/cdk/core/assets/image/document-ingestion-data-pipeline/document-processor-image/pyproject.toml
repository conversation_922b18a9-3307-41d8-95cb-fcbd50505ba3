[tool.poetry]
name = "document-conversion"
version = "0.1.0"
description = ""
authors = ["cancelself <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
docling = "^2"
fastapi = "^0"
httpx = "^0"
markitdown = "^0"
python = ">=3.10.12,<3.14"
python-multipart = "^0"
uvicorn = "^0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.packages]]
from = 'src'
include = 'document_processor'

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
