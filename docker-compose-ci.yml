services:

    activemq:
        build:
            context: ./activemq
            dockerfile: Dockerfile
        env_file:
            - ./docker/activemq/activemq.env
        volumes:
            - activemq-data:/var/lib/activemq/data/
        ports:
            - "61616:61616"
            - "61617:61617"
            - "8161:8161"

    aws:
        extends:
            file: docker-compose-local-aws.yml
            service: localstack

    mongo:
      image: mongo:8
      container_name: mongo
      restart: always
      ports:
        - "27017:27017"
      env_file:
        - ./docker/mongo/mongo.env
      volumes:
        - mongo-data:/data/db

    opensearch:
      image: opensearchproject/opensearch:latest
      env_file:
        - ./docker/opensearch/opensearch.env
      ports:
        - "9200:9200"  # OpenSearch REST API
        - "9600:9600"  # OpenSearch performance analyzer
      volumes:
        - opensearch-data:/usr/share/opensearch/data
      restart: always

    postgres-ci:
      image: "postgres:16.4" # use the same version as our AWS instance
      env_file:
        - ./docker/postgres/postgres.env
      command: postgres -c 'config_file=/etc/postgresql/postgresql.conf'
      volumes:
        - ./docker/postgres/custom-postgresql.conf:/etc/postgresql/postgresql.conf
      hostname: postgres
      shm_size: '2gb'
      ports:
        - "5433:5432"

    postgres-prefect:
      image: "postgres:16.4" # use the same version as our AWS instance
      restart: always
      env_file:
        - ./docker/postgres/postgres-prefect.env
      volumes:
        - postgres-prefect-data:/var/lib/postgresql/data
      ports:
        - "5434:5432"

    prefect:
      restart: always
      image: prefecthq/prefect:3-python3.12
      env_file:
        - ./docker/prefect/prefect.env
      command: prefect server start --host 0.0.0.0
      ports:
        - "4200:4200"  # Prefect server UI and API
      volumes:
        - postgres-prefect-data:/data  # Persistent storage
      depends_on:
        - postgres-prefect

    redis:
      image: redis:7.4-alpine
      volumes:
        # Mount the custom config read-only
        - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
        # Data directory for AOF / RDB files
        - redis-data:/data
      command: >
        redis-server /usr/local/etc/redis/redis.conf
      hostname: redis
      ports:
        - "6379:6379"
      restart: always

volumes:
    activemq-data:
    mongo-data:
    opensearch-data:
    postgres-prefect-data:
    redis-data:


