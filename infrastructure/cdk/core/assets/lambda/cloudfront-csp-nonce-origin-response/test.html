<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
        <title>Welcome to Unblocked! // unblocked documentation</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="description" content />
        <meta name="generator" content="GitBook 5.0.0" />
        <meta name="author" content="The unblocked team" />

        <link rel="stylesheet" href="gitbook/style.css" />

        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />

        <link rel="stylesheet" href="gitbook/gitbook-plugin-tabs2/tabs.css" />

        <link rel="stylesheet" href="gitbook/gitbook-plugin-intopic-toc/style.css" />

        <link rel="stylesheet" href="gitbook/@honkit/honkit-plugin-highlight/website.css" />

        <link rel="stylesheet" href="gitbook/gitbook-plugin-search/search.css" />

        <link rel="stylesheet" href="_js/highlight/styles/default.css" />
        <link rel="stylesheet" href="_js/highlight/styles/github.css" />

        <link rel="stylesheet" href="./_css/website.css" />

        <meta name="HandheldFriendly" content="true" />
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <link
            rel="apple-touch-icon-precomposed"
            sizes="152x152"
            href="gitbook/images/apple-touch-icon-precomposed-152.png"
        />
        <link rel="shortcut icon" href="_img/favicon.ico" type="image/x-icon" />

        <link rel="next" href="overview/" />

        <meta property="og:title" content="unblocked Documentation" />
        <meta property="og:description" content="Documentation for unblocked's CI/CD platform." />
        <meta property="og:site_name" content="unblocked" />
        <link rel="canonical" href="https://docs.unblocked.com/" />
    </head>
    <body>
        <div class="book with-summary">
            <div class="book-summary">
                <div class="logo">
                    <a href="./index.html">
                        <img src="_img/logo-unblocked.png" alt="unblocked docs" />
                    </a>
                </div>

                <nav role="navigation">
                    <ul class="summary">
                        <li class="chapter active" data-level="1.1" data-path="README.adoc">
                            <a href="./index.html"> Welcome to Unblocked! </a>
                        </li>

                        <li class="header">Overview</li>

                        <li class="chapter" data-level="2.1" data-path="overview/README.adoc">
                            <a href="overview/index.html"> What is Unblocked? </a>
                        </li>

                        <li class="chapter" data-level="2.2" data-path="overview/quickstart.adoc">
                            <a href="overview/quickstart.html"> Quickstart Guide </a>
                        </li>

                        <li class="header">Walkthroughs</li>

                        <li class="chapter" data-level="3.1" data-path="walkthroughs/question.adoc">
                            <a href="walkthroughs/question.html"> Ask Unblocked a Question </a>
                        </li>

                        <li class="chapter" data-level="3.2" data-path="walkthroughs/discover.adoc">
                            <a href="walkthroughs/discover.html"> Discover Insights </a>
                        </li>

                        <li class="chapter" data-level="3.3" data-path="walkthroughs/notes.adoc">
                            <a href="walkthroughs/notes.html"> Create Notes </a>
                        </li>

                        <li class="chapter" data-level="3.4" data-path="walkthroughs/videos.adoc">
                            <a href="walkthroughs/videos.html"> Create Videos </a>
                        </li>

                        <li class="chapter" data-level="3.5" data-path="walkthroughs/manage.adoc">
                            <a href="walkthroughs/manage.html"> Manage Components </a>
                        </li>

                        <li class="header">Product Guides</li>

                        <li class="chapter" data-level="4.1" data-path="productGuides/mac.adoc">
                            <a href="productGuides/mac.html"> macOS Menu Bar App </a>
                        </li>

                        <li class="chapter" data-level="4.2" data-path="productGuides/ide.adoc">
                            <a href="productGuides/ide.html"> IDE Plugins </a>
                        </li>

                        <li class="chapter" data-level="4.3" data-path="productGuides/dashboard.adoc">
                            <a href="productGuides/dashboard.html"> Web Dashboard </a>
                        </li>

                        <li class="header">Personal Settings</li>

                        <li class="chapter" data-level="5.1" data-path="personalSettings/profile.adoc">
                            <a href="personalSettings/profile.html"> My Profile </a>
                        </li>

                        <li class="chapter" data-level="5.2" data-path="personalSettings/connectedAccounts.adoc">
                            <a href="personalSettings/connectedAccounts.html"> Connected Accounts </a>
                        </li>

                        <li class="chapter" data-level="5.3" data-path="personalSettings/email.adoc">
                            <a href="personalSettings/email.html"> Email Preferences </a>
                        </li>

                        <li class="header">Team Settings</li>

                        <li class="chapter" data-level="6.1" data-path="teamSettings/components.adoc">
                            <a href="teamSettings/components.html"> Manage Components </a>
                        </li>

                        <li class="chapter" data-level="6.2" data-path="teamSettings/integrations.adoc">
                            <a href="teamSettings/integrations.html"> Integrations </a>

                            <ul class="articles">
                                <li class="chapter" data-level="6.2.1" data-path="teamSettings/github.adoc">
                                    <a href="teamSettings/github.html"> GitHub.com </a>

                                    <ul class="articles">
                                        <li
                                            class="chapter"
                                            data-level="6.2.1.1"
                                            data-path="teamSettings/github_connect.adoc"
                                        >
                                            <a href="teamSettings/github_connect.html"> Connect Your Repositories </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.1.2"
                                            data-path="teamSettings/github_manage.adoc"
                                        >
                                            <a href="teamSettings/github_manage.html"> Manage Your Repositories </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.1.3"
                                            data-path="teamSettings/github_connect_ide.adoc"
                                        >
                                            <a href="teamSettings/github_connect_ide.html">
                                                Connect Repositories from your IDE
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="chapter" data-level="6.2.2" data-path="teamSettings/bitbucket.adoc">
                                    <a href="teamSettings/bitbucket.html"> Bitbucket.org </a>

                                    <ul class="articles">
                                        <li
                                            class="chapter"
                                            data-level="6.2.2.1"
                                            data-path="teamSettings/bitbucket_connect.adoc"
                                        >
                                            <a href="teamSettings/bitbucket_connect.html">
                                                Connect Your Repositories
                                            </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.2.2"
                                            data-path="teamSettings/bitbucket_manage.adoc"
                                        >
                                            <a href="teamSettings/bitbucket_manage.html"> Manage Your Repositories </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.2.3"
                                            data-path="teamSettings/bitbucket_connect_ide.adoc"
                                        >
                                            <a href="teamSettings/bitbucket_connect_ide.html">
                                                Connect Repositories from your IDE
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="chapter" data-level="6.2.3" data-path="teamSettings/confluence.adoc">
                                    <a href="teamSettings/confluence.html"> Confluence </a>

                                    <ul class="articles">
                                        <li
                                            class="chapter"
                                            data-level="6.2.3.1"
                                            data-path="teamSettings/confluence_connect.adoc"
                                        >
                                            <a href="teamSettings/confluence_connect.html"> Connect Your Spaces </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.3.2"
                                            data-path="teamSettings/confluence_manage.adoc"
                                        >
                                            <a href="teamSettings/confluence_manage.html"> Manage Spaces </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="chapter" data-level="6.2.4" data-path="teamSettings/jira.adoc">
                                    <a href="teamSettings/jira.html"> Jira </a>

                                    <ul class="articles">
                                        <li
                                            class="chapter"
                                            data-level="6.2.4.1"
                                            data-path="teamSettings/jira_connect.adoc"
                                        >
                                            <a href="teamSettings/jira_connect.html"> Connect Your Projects </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.4.2"
                                            data-path="teamSettings/jira_manage.adoc"
                                        >
                                            <a href="teamSettings/jira_manage.html"> Manage Projects </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="chapter" data-level="6.2.5" data-path="teamSettings/slack.adoc">
                                    <a href="teamSettings/slack.html"> Slack </a>

                                    <ul class="articles">
                                        <li
                                            class="chapter"
                                            data-level="6.2.5.1"
                                            data-path="teamSettings/slack_connect.adoc"
                                        >
                                            <a href="teamSettings/slack_connect.html"> Connect Your Channels </a>
                                        </li>

                                        <li
                                            class="chapter"
                                            data-level="6.2.5.2"
                                            data-path="teamSettings/slack_manage.adoc"
                                        >
                                            <a href="teamSettings/slack_manage.html"> Manage Channels </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="chapter" data-level="6.2.6" data-path="teamSettings/stack_overflow.adoc">
                                    <a href="teamSettings/stack_overflow.html"> Stack Overflow for Teams </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>

            <div class="book-body">
                <div class="body-inner">
                    <div class="book-header" role="navigation">
                        <div id="book-search-input" role="search">
                            <input type="text" tabindex="1" placeholder="Type to search" />
                        </div>

                        <!-- Title -->
                        <h1></h1>
                    </div>

                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            <div id="book-search-results">
                                <div class="search-noresults">
                                    <section class="normal markdown-section">
                                        <h1 id="welcome-to-unblocked">Welcome to Unblocked!</h1>
                                        <div id="preamble">
                                            <div class="sectionbody">
                                                <div class="paragraph">
                                                    <p>
                                                        Unblocked gives developers fast and helpful answers to questions
                                                        specific to their codebase.
                                                    </p>
                                                </div>
                                                <div class="paragraph">
                                                    <p>
                                                        You can think of Unblocked as a virtual member of your team that
                                                        can answer any question about any part of your codebase.
                                                    </p>
                                                </div>
                                                <div class="paragraph">
                                                    <p>Here’s a quick overview of Unblocked in less than 2 minutes…​</p>
                                                </div>
                                                <div class="videoblock">
                                                    <div class="content">
                                                        <video src="_video/create-note.mp4" controls>
                                                            Your browser does not support the video tag.
                                                        </video>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="sect1">
                                            <h2 id="_get_started">Get Started</h2>
                                            <div class="sectionbody">
                                                <div class="paragraph">
                                                    <p>
                                                        We’ve put together a guide for you to get setup quickly and
                                                        easily.
                                                    </p>
                                                </div>
                                                <div class="sidebarblock link-card">
                                                    <div class="content">
                                                        <div class="title">
                                                            <a href="overview/quickstart.html">Quickstart Guide</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </section>
                                </div>
                                <div class="search-results">
                                    <div class="has-results">
                                        <h1 class="search-results-title">
                                            <span class="search-results-count"></span> results matching "<span
                                                class="search-query"
                                            ></span
                                            >"
                                        </h1>
                                        <ul class="search-results-list"></ul>
                                    </div>
                                    <div class="no-results">
                                        <h1 class="search-results-title">
                                            No results matching "<span class="search-query"></span>"
                                        </h1>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-navigation">
                                <a
                                    href="overview/index.html"
                                    class="bottom-navigation---next-link navigation navigation-next navigation-unique"
                                    aria-label="Next page: What is Unblocked?"
                                >
                                    <div>
                                        <div class="bottom-navigation--next-sublabel">Next</div>
                                        <div class="bottom-navigation--next-label">What is Unblocked?</div>
                                    </div>
                                    <i class="fa fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="gitbook_page_info">
                {"page":{"title":"Welcome to Unblocked!","level":"1.1","depth":1,"next":{"title":"What is
                Unblocked?","level":"2.1","depth":1,"path":"overview/README.adoc","ref":"overview/README.adoc","articles":[]},"dir":"ltr"},"config":{"plugins":["explicit-url@0.0.3","tabs2","-theme-default","theme-unblocked","-fontsettings","-sharing","-navigation","intopic-toc"],"styles":{"website":"_css/website.css"},"pluginsConfig":{"intopic-toc":{"selector":".markdown-section
                h1, .markdown-section h2, .markdown-section h3, .markdown-section h4, .markdown-section h5,
                .markdown-section
                h6","mode":"flat","maxDepth":6,"isCollapsed":true,"isScrollspyActive":false,"visible":true,"label":""},"explicit-url":{},"tabs2":{},"highlight":{},"search":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"theme-unblocked":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false}},"theme":"default","author":"The
                unblocked
                team","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56},"embedFonts":false},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"language":"en","gitbook":"*","description":"Documentation
                for
                unblocked"},"file":{"path":"README.adoc","mtime":"2023-08-28T19:46:48.264Z","type":"asciidoc"},"gitbook":{"version":"5.0.0","time":"2023-08-28T19:47:23.729Z"},"basePath":".","book":{"language":""}}
            </div>
            <script src="_js/change.js"></script>
        </div>

        <script src="gitbook/gitbook.js"></script>
        <script src="gitbook/theme.js"></script>

        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>

        <script src="gitbook/gitbook-plugin-tabs2/tabs.js"></script>

        <script src="gitbook/gitbook-plugin-intopic-toc/anchor.min.js"></script>

        <script src="gitbook/gitbook-plugin-intopic-toc/gumshoe.polyfills.min.js"></script>

        <script src="gitbook/gitbook-plugin-intopic-toc/plugin.js"></script>

        <script src="gitbook/gitbook-plugin-search/search-engine.js"></script>

        <script src="gitbook/gitbook-plugin-search/search.js"></script>

        <script src="gitbook/gitbook-plugin-lunr/lunr.min.js"></script>

        <script src="gitbook/gitbook-plugin-lunr/search-lunr.js"></script>

        <script src="_js/highlight/highlight.pack.js"></script>
        <script src="_js/docs.js"></script>
    </body>
</html>
