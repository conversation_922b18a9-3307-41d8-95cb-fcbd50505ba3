/*
 * This lambda function executes on viewer response events
 * It looks for a custom header which contains the nonce value. If found
 * it will then inject the value into the csp header
 */
exports.handler = async (event) => {
    const response = event.Records[0].cf.response;

    // Check if the custom header X-Custom-Header exists
    if (response.headers['x-custom-header']) {
        const nonce = response.headers['x-custom-header'][0].value;

        // Check if the response has a CSP header
        if (response.headers['content-security-policy']) {
            const cspHeaderValue = response.headers['content-security-policy'][0].value;

            // Modify the CSP header to include the nonce
            const modifiedCspHeaderValue = cspHeaderValue.replace(/script-src/i, `script-src 'nonce-${nonce}'`);

            // Update the response's CSP header with the modified value
            response.headers['content-security-policy'][0].value = modifiedCspHeaderValue;
        }
    }

    return response;
};
