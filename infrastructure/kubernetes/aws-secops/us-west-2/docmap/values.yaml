fullnameOverride: "docmap"

replicaCount: 3

# Container images configuration
image:
  tgi:
    repository: "877923746456.dkr.ecr.us-west-2.amazonaws.com/onprem-ml-docmap"
    tag: "1-b0ff22dbf76c967de936520f0ca2b14f83856c15"
    pullPolicy: IfNotPresent

#No Environment variables needed for DocMap
#Env:
#  - name: NVIDIA_DRIVER_CAPABILITIES
#    value: "compute,utility"
#  - name: DISABLE_CUSTOM_KERNELS
#    value: "true"
#  - name: NVIDIA_REQUIRE_CUDA
#    value: "cuda>=8.0"
#  - name: HF_HUB_CACHE
#    value: "/hf-snapshots"
#  - name: HUGGINGFACE_HUB_CACHE
#    value: "/hf-snapshots"

# Resource configuration for DocMap
resources:
  nginx:
    requests:
      cpu: "2"
      memory: "2Gi"
    limits:
      cpu: "3"
      memory: "3Gi"
  tgi:
    requests:
      cpu: "5"
      memory: "12Gi"
    limits:
      cpu: "6"
      memory: "14Gi"
    shm:
      size: 512Mi

# Health check probes for DocMap
startupProbe:
  httpGet:
    path: /ping
    port: 8080
  failureThreshold: 30
  periodSeconds: 10
  initialDelaySeconds: 120

readinessProbe:
  httpGet:
    path: /ping
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 120
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 60
  successThreshold: 1

# Ingress configuration for DocMap
ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/group.order: "400"
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: instance
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/group.name: "ml-internal"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    external-dns.alpha.kubernetes.io/hostname: ml.alb.secops-2.us-west-2.secops.getunblocked.com
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:877923746456:certificate/08dcc91a-2501-4676-a004-714001a2c97f
  paths:
    - host: ml.alb.secops-2.us-west-2.secops.getunblocked.com
      path: /api/ml/models/doc-markdown
      pathType: Prefix
    - host: ml.alb.secops.getunblocked.com
      path: /api/ml/models/doc-markdown
      pathType: Prefix

# Node scheduling
#tolerations:
#  - key: "nvidia.com/gpu"
#    operator: "Equal"
#    value: "true"
#    effect: "NoSchedule"

# We are using built-in labels which are set by Nvidia GPU operator on EKS
nodeSelector:
  "node.kubernetes.io/instance-type": c5a.2xlarge

# Persistent volume configuration
pvc:
  enabled: false
#  accessModes:
#    - ReadWriteMany
#  storage: 100Gi
#  storageClassName: efs-sc

keda:
  enabled: false
#  pollingInterval: 60  # seconds
#  cooldownPeriod: 300  # seconds
#  minReplicaCount: 4
#  maxReplicaCount: 6
#  triggers:
#    cpu:
#      enabled: false
#      metricType: "Utilization"
#      value: "70"
#    cloudwatch:
#      - enabled: true
#        identityOwner: "operator"
#        awsRegion: "us-west-2"
#        expression: SELECT SUM(RequestCountPerTarget) FROM SCHEMA("AWS/ApplicationELB", TargetGroup) WHERE TargetGroup = 'targetgroup/k8s-default-e5mistra-cb1b5499bb/c81115453220367d'
#        metricStat: "Sum"
#        metricStatPeriod: "60"
#        targetMetricValue: "150"
#        metricCollectionTime: "60"
#        metricEndTimeOffset: "60"
#        minMetricValue: "0"
