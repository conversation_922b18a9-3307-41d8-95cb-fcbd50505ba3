# VPC
module "vpc" {
  source  = "terraform-google-modules/network/google"
  version = "11.1.1"

  project_id   = var.project_id
  network_name = var.vpc_name
  routing_mode = "REGIONAL"

  subnets = [
    {
      subnet_name   = local.public_subnet_name
      subnet_ip     = var.public_subnet_cidr
      subnet_region = var.region
      description   = "Public subnet with internet access"
    },
    {
      subnet_name           = local.private_subnet_name
      subnet_ip             = var.private_subnet_cidr
      subnet_region         = var.region
      description           = "Private subnet with NAT"
      subnet_private_access = "true"
    }
  ]

  secondary_ranges = {
    (local.private_subnet_name) = [
      {
        range_name    = "${local.private_subnet_name}-${var.gke_name}-pods"
        ip_cidr_range = var.gke_pods_cidr
      },
      {
        range_name    = "${local.private_subnet_name}-${var.gke_name}-services"
        ip_cidr_range = var.gke_services_cidr
      }
    ]
  }

  routes = [
    {
      name              = "egress-internet"
      description       = "Default route to Internet"
      destination_range = "0.0.0.0/0"
      tags              = "public"
      next_hop_internet = "true"
    }
  ]
}

# Cloud NAT
module "cloud_nat" {
  source  = "terraform-google-modules/cloud-nat/google"
  version = "5.3.0"

  project_id = var.project_id
  region     = var.region
  router     = "${var.vpc_name}-router"
  network    = module.vpc.network_name

  create_router                      = true
  nat_ips                            = [google_compute_address.nat_ip.self_link]
  source_subnetwork_ip_ranges_to_nat = "LIST_OF_SUBNETWORKS"

  subnetworks = [
    {
      name                     = local.private_subnet_name
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    }
  ]

  depends_on = [module.vpc, google_compute_address.nat_ip]
}

# static IP for the NAT
resource "google_compute_address" "nat_ip" {
  name   = "nat-external-ip"
  region = var.region
}

# static IP for the VPN
resource "google_compute_address" "vpn_ip" {
  name   = "vpn-ip"
  region = var.region
}

# static IP for the TEMP
resource "google_compute_address" "temp_ip" {
  name   = "temp-ip"
  region = var.region
}

# static IP for GHE
resource "google_compute_address" "ghe_ip" {
  name   = "ghe-ip"
  region = var.region
}

# static IP for the GKE Gateway
resource "google_compute_global_address" "gke_gateway" {
  name    = "gke-secops-gateway"
  project = var.project_id
}
