@startuml

title "Triage Sequence"

participant WebhookService
queue HooksCi
participant CiWebhookHandler
queue CiEvent
participant BitbucketBuildEventHandler
participant GitHubCheckSuiteEventHandler
participant BuildIngestionService
participant BuildProcessService
database PullRequestModel
database BuildModel
database BuildJobModel
database BuildTriageModel
queue CiTriages as CiPipeline
participant TriageRequestEventHandler
participant BuildTriageService
participant TriagePublishEventHandler
participant TriageBillingEventHandler
participant OrgBillingService
participant GitHub

group webhooks
    --> WebhookService ++ : webhook
    WebhookService -> HooksCi : payload
    return accepted
end group

group bitbucket: build status
    HooksCi --> CiWebhookHandler ++ : on build status
        CiWebhookHandler -> CiEvent : BitbucketBuildEvent
    return
    CiEvent -> BitbucketBuildEventHandler ++
        BitbucketBuildEventHandler -> BuildIngestionService ++
            BuildIngestionService -> BuildModel : upsert
            BuildIngestionService -> BuildJobModel : upsert(jobs)
            BuildIngestionService -> BuildProcessService : processBuild
        return
    return
end group

group github: check_suite
    HooksCi --> CiWebhookHandler ++ : on check suite
    CiWebhookHandler -> CiEvent : GitHubCheckSuiteEvent
    return
    CiEvent -> GitHubCheckSuiteEventHandler ++
        GitHubCheckSuiteEventHandler -> BuildIngestionService ++
            BuildIngestionService -> BuildModel : upsert
            BuildIngestionService -> BuildJobModel : upsert(jobs)
            BuildIngestionService -> BuildProcessService : processBuild
        return
    return
end group

group BuildProcessService : processBuild
    BuildIngestionService -> BuildProcessService ++
        alt onSuccess
            BuildProcessService -> BuildTriageModel : findTriageStateFor pullRequest
            BuildProcessService -> PullRequestModel : updateCiTriage
            BuildProcessService -> CiPipeline : TriagePublish
        end alt
        alt onFailure
            BuildProcessService -> CiPipeline : TriageRequest
        end group
    return
end group

group TriageRequest
    CiPipeline -> TriageRequestEventHandler ++ : TriageRequest
        TriageRequestEventHandler -> BuildTriageService ++ : generate triage
            BuildTriageService -> BuildTriageService : pipeline
            BuildTriageService -> BuildTriageModel : markTriagesOnFailure
            BuildTriageService -> BuildTriageModel : save new triage
            BuildTriageService -> PullRequestModel : updateCiTriage Open
            BuildTriageService -> CiPipeline : TriagePublish
        return
    return
end group

group TriagePublish
    CiPipeline -> TriagePublishEventHandler ++ : TriagePublish
        alt onTriageCreate
            TriagePublishEventHandler -> GitHub : pullRequestCommentCreate
        end alt
        alt onTriageUpdate
            TriagePublishEventHandler -> GitHub : pullRequestCommentUpdate
        end alt
        TriagePublishEventHandler -> CiPipeline : TriageBilling
    return
end group

group TriageBilling
    CiPipeline -> TriageBillingEventHandler ++ : TriageBilling
        alt when more than 5 comments
            TriageBillingEventHandler -> OrgBillingService : assignSeat
        end alt
    return
end group

@enduml
