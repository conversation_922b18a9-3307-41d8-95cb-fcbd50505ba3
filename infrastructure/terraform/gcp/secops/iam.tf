# 1) Service Account for GitHub Actions
resource "google_service_account" "gha" {
  account_id   = local.gha_sa_name
  display_name = "${local.gha_sa_name} SA for GKE ${module.gke.name} cluster"
}

# 2) Grant minimal IAM on the project so the SA can run `gcloud container clusters get-credentials`
#    This lets it discover the cluster & fetch an access token; actual kubectl permissions are via K8s RBAC.
resource "google_project_iam_member" "gha_cluster_view" {
  project = var.project_id
  role    = "roles/container.clusterViewer"
  member  = "serviceAccount:${google_service_account.gha.email}"
}

# (Optional) If you want it to manage node pools/etc. via API, grant admin (broader):
#   role    = "roles/container.admin"

# 3) Create a JSON key for the SA (store securely; rotate!)
resource "google_service_account_key" "gha_key" {
  service_account_id = google_service_account.gha.name
  keepers = {
    # change this to rotate the key intentionally
    purpose = local.gha_sa_name
  }
}

resource "google_project_service" "required" {
  for_each           = toset(local.services)
  project            = var.project_id
  service            = each.key
  disable_on_destroy = false
}

# Grant minimal read so gcloud can look up the project/org tree cleanly
# roles/browser ⊇ resourcemanager.projects.get and basic metadata listing
resource "google_project_iam_member" "ci_browser" {
  project = var.project_id
  role    = "roles/browser"
  member  = "serviceAccount:${google_service_account.gha.email}"

  depends_on = [google_project_service.required]
}

# The Role
resource "kubernetes_role" "deployer" {
  metadata {
    name      = "deployer-role"
    namespace = "default"
  }

  rule {
    api_groups = [
      "",
      "apps",
      "batch",
      "extensions",
      "networking.k8s.io",
      "secrets-store.csi.x-k8s.io",
      "crd.projectcalico.org",
      "keda.sh",
      "autoscaling",
      "external-secrets.io",
      "rbac.authorization.k8s.io",
      "gateway.networking.k8s.io",
      "networking.gke.io",
      "policy"
    ]
    resources = [
      "configmaps",
      "cronjobs",
      "deployments",
      "events",
      "ingresses",
      "jobs",
      "pods",
      "pods/attach",
      "pods/exec",
      "pods/log",
      "pods/portforward",
      "secrets",
      "services",
      "replicasets",
      "networkpolicies",
      "secretproviderclasses",
      "scaledobjects",
      "horizontalpodautoscalers",
      "externalsecrets",
      "roles",
      "rolebindings",
      "serviceaccounts",
      "persistentvolumeclaims",
      "statefulsets",
      "httproutes",
      "healthcheckpolicies",
      "poddisruptionbudgets"
    ]
    verbs = [
      "create",
      "delete",
      "describe",
      "get",
      "list",
      "patch",
      "update"
    ]
  }
}

resource "kubernetes_role_binding" "gha_ns_edit" {
  metadata {
    name      = local.gha_sa_name
    namespace = "default"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.deployer.metadata[0].name
  }
  subject {
    kind      = "User"
    name      = google_service_account.gha.email
    api_group = "rbac.authorization.k8s.io"
  }

  depends_on = [module.gke]
}