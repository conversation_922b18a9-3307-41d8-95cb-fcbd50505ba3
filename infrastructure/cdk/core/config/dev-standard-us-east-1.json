{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-east-1", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "route53HostedZoneID": "Z05916033T74B60YGQ2P2", "route53HostedZoneName": "dev.getunblocked.com", "cdkAppInfo": {"app": "core-aws-stack", "environment": "dev", "version": "0.0.0", "build": "0"}, "dns": {"route53HostedZoneID": "Z05916033T74B60YGQ2P2", "route53HostedZoneName": "dev.getunblocked.com", "cnameRecords": [{"recordName": "alb.dev.getunblocked.com", "domainName": "alb.us-west-2.dev.getunblocked.com", "comment": "cname record pointing to dev public alb endpoint"}, {"recordName": "ml.alb.dev.getunblocked.com", "domainName": "ml-alb-us-west-2.dev.getunblocked.com", "comment": "cname record pointing to dev public alb endpoint"}, {"recordName": "admin.dev.getunblocked.com", "domainName": "adminweb.us-west-2.dev.getunblocked.com", "comment": "cname record pointing to internal admin service ALB endpoint"}]}, "certRequests": [{"name": "CloudFrontCert", "domainName": "dev.getunblocked.com", "subjectAlternativeNames": ["dev.getunblocked.com", "www.dev.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "RedirectCloudFrontCert", "domainName": "redirect.dev.getunblocked.com", "subjectAlternativeNames": ["*.dev.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "DocsCloudFrontCert", "domainName": "dev.getunblocked.com", "subjectAlternativeNames": ["docs.dev.getunblocked.com"], "addRegionWildcardAsSan": false, "useEmailValidation": true}], "webAclCloudFront": {"name": "waf-cloudfront", "endpointIPFilterRules": [{"name": "github-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/github"], "ipv4CIDRWhiteList": ["************/22", "*************/22", "************/20", "***********/20"]}, {"name": "gitlab-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/gitlab"], "ipv4CIDRWhiteList": ["***********/28", "***********/24"]}, {"name": "bitbucket-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/bitbucket"], "ipv4CIDRWhiteList": ["**********/28", "************/28", "*************/28", "*************/28", "*************/28", "*************/28", "**************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28", "***************/28"]}, {"name": "assemblyai-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/transcription"], "ipv4CIDRWhiteList": ["************/32"]}, {"name": "stripe-webhook-ip-filter", "positionalConstraint": "EXACTLY", "searchStrings": ["/api/hooks/stripe"], "ipv4CIDRWhiteList": ["**********/32", "*************/32", "*************/32", "**************/32", "*************/32", "**************/32", "************/32", "*************/32", "*************/32", "**************/32", "**************/32", "*************/32"]}], "rateBasedRules": [{"name": "PusherPerUserRateLimit", "block": false, "limit": 1000, "positionalConstraint": "STARTS_WITH", "searchString": "/api/channels/", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "PusherPerClientPerUserRateLimit", "block": true, "limit": 1500, "positionalConstraint": "STARTS_WITH", "searchString": "/api/channels/", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "LogsPerClientPerUserRateLimit", "block": true, "limit": 500, "positionalConstraint": "STARTS_WITH", "searchString": "/api/logs", "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "ApiPerClientPerUserRateLimit", "block": true, "limit": 1500, "positionalConstraint": "STARTS_WITH", "searchString": "/api/", "excludedSearchStrings": [{"positionalConstraint": "STARTS_WITH", "searchString": "/api/logs"}, {"positionalConstraint": "STARTS_WITH", "searchString": "/api/channels/"}], "aggregateKeyType": "CUSTOM_KEYS", "aggregateKeyHeaders": ["x-unblocked-client-id", "x-unblocked-subject"], "headerTextTransformationType": "LOWERCASE"}, {"name": "PublicApiRateLimit", "block": true, "limit": 2000, "positionalConstraint": "STARTS_WITH", "searchString": " /api/v1/", "aggregateKeyType": "IP", "headerTextTransformationType": "LOWERCASE"}, {"name": "ApiPerIPRateLimit", "block": false, "limit": 1500, "positionalConstraint": "STARTS_WITH", "searchString": "/api/", "aggregateKeyType": "IP"}]}, "cloudFront": {"domainNames": ["dev.getunblocked.com", "www.dev.getunblocked.com"], "certName": "CloudFrontCert", "csp": ["default-src 'self' https://*.clarity.ms https://c.bing.com", "img-src blob: data: https:", "media-src https:", "style-src 'unsafe-inline' https:", "script-src 'self' https://*.googletagmanager.com https://*.intercom.io https://*.intercomcdn.com https://*.segment.io https://*.umami.is https://*.licdn.com https://*.segment.com https://*.redditstatic.com https://js.stripe.com https://*.js.stripe.com https://*.googleapis.com https://cdn.syftdata.com https://cdn.jsdelivr.net", "worker-src 'self' blob:", "child-src 'self' blob:", "frame-src https://*.js.stripe.com https://js.stripe.com https://hooks.stripe.com https://app.vidzflow.com", "connect-src 'self' https://*.google-analytics.com https://*.intercom.io https://*.intercomcdn.com https://*.segment.io https://*.sentry.io wss://*.intercom.io https://user-image-assets-prod-us-west-2.s3.us-west-2.amazonaws.com https://user-image-assets-prod-us-east-2.s3.us-east-2.amazonaws.com https://user-image-assets-dev-us-west-2.s3.us-west-2.amazonaws.com https://user-image-assets-dev-us-east-2.s3.us-east-2.amazonaws.com https://*.umami.dev https://*.licdn.com https://*.ads.linkedin.com https://*.segment.com https://*.redditstatic.com https://pixel-config.reddit.com https://maps.googleapis.com https://api.stripe.com https://use.typekit.net", "font-src 'self' data: https:", "block-all-mixed-content"]}, "docsCloudFront": {"domainNames": ["docs.dev.getunblocked.com"], "certName": "DocsCloudFrontCert"}, "subdomainRedirectCloudFront": {"domainNames": ["redirect.dev.getunblocked.com", "*.dev.getunblocked.com"], "certName": "RedirectCloudFrontCert"}, "staticSites": [{"name": "dashboard", "pathPattern": "/dashboard/*", "s3BucketName": "dashboard.dev.getunblocked.com", "enableVersioning": true}, {"name": "download-assets", "pathPattern": "/download-assets/*", "s3BucketName": "download-assets.dev.getunblocked.com", "enableVersioning": true}, {"name": "releases", "pathPattern": "/releases/*", "s3BucketName": "releases.dev.getunblocked.com", "enableVersioning": true}, {"name": "landing-page", "pathPattern": "/*", "s3BucketName": "landing-page.dev.getunblocked.com", "enableVersioning": true}, {"name": "docs", "pathPattern": "/docs/*", "s3BucketName": "docs.dev.getunblocked.com", "enableVersioning": true}]}