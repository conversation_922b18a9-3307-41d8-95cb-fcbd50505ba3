'use strict';
var ServerMock = require('mock-http-server');
const assert = require('assert').strict;
const lambdaLocal = require('lambda-local');
const path = require('path');

// Lambda sample test event to be used as template
const testEventTemplate = require('./test-event.json');

// Mock server params
const mockValidHTTP200Response = require('./mock-dl-url-response.json');
const mockHTTPServerPort = 8080;
const mockHTTPServerHost = 'localhost';
const mockValid200Path = '/api/assets/teams/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035';
const mockValid404Path = '/api/assets/teams/c9c47779-235f-4ca9-aefb-96758fa96650/e95a4741-4bdd-4f4c-a741-21c5572f64f1';
const mockValid401Path = '/api/assets/teams/c9c47779-235f-4ca9-aefb-96758fa96650/45d9cde9-2877-417e-b8bc-91193ce6aecb';
const mockValid500Path = '/api/assets/teams/c9c47779-235f-4ca9-aefb-96758fa96650/978b666d-c456-4b1a-b57d-1c788d3c776b';

describe('Test', function () {
    // Run an HTTP server on localhost
    var server = new ServerMock({ host: mockHTTPServerHost, port: mockHTTPServerPort });

    beforeEach(function (done) {
        server.on({
            method: 'GET',
            path: mockValid200Path,
            reply: {
                status: 200,
                headers: { 'content-type': 'application/json' },
                body: JSON.stringify(mockValidHTTP200Response),
            },
        });

        server.on({
            method: 'GET',
            path: mockValid404Path,
            reply: {
                status: 404,
                headers: { 'content-type': 'application/json' },
                body: JSON.stringify({}),
            },
        });

        server.on({
            method: 'GET',
            path: mockValid401Path,
            reply: {
                status: 401,
                headers: { 'content-type': 'application/json' },
                body: JSON.stringify({}),
            },
        });

        server.on({
            method: 'GET',
            path: mockValid500Path,
            reply: {
                status: 500,
                headers: { 'content-type': 'application/json' },
                body: JSON.stringify({}),
            },
        });
        server.start(done);
    });

    afterEach(function (done) {
        server.stop(done);
    });

    it('Valid HTTP request - expects pass', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri =
            '/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035';
        assert.equal((await runLambda(testEvent)).status, undefined);
    });

    it('Invalid URIs - expects 400', async function () {
        const uriList = [
            '/invalidTeamID/123e4567-e89b-12d3-a456-426614174035', // Invalid team ID
            '/c9c47779-235f-4ca9-aefb-96758fa96650/invalidAssetID', // Invalid asset ID
            '/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035/extraPart', // Path has extra part
            '/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035/extraPart', // Path must start with /assets/
            '/assets/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035/extraPart', // Path must not start with /assets
            '/dummy/c9c47779-235f-4ca9-aefb-96758fa96650/123e4567-e89b-12d3-a456-426614174035/extraPart', // Path must not have more than two parts
        ];

        for (let uri of uriList) {
            let testEvent = clone(testEventTemplate);
            testEvent.Records[0].cf.request.uri = uri;
            assert.equal((await runLambda(testEvent)).status, '400');
        }
    });

    it('Missing auth query string - expects 401', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.querystring = '';
        assert.equal((await runLambda(testEvent)).status, undefined);
        assert.equal(
            (await runLambda(testEvent)).origin.custom.domainName,
            testEvent.Records[0].cf.request.headers.host[0].value
        );
        assert.equal((await runLambda(testEvent)).uri, `/error-assets/error-401.png`);
    });

    it('404 from assets service - expects 200 with error payload', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri =
            '/c9c47779-235f-4ca9-aefb-96758fa96650/e95a4741-4bdd-4f4c-a741-21c5572f64f1';
        //assert.equal((await runLambda(testEvent)).status, '404');
        const resp = await runLambda(testEvent);
        assert.equal(resp.status, undefined);
        assert.equal(resp.origin.custom.domainName, testEvent.Records[0].cf.request.headers.host[0].value);
        assert.equal(resp.uri, `/error-assets/error-404.png`);
    });

    it('401 from assets service - expects 401', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri =
            '/c9c47779-235f-4ca9-aefb-96758fa96650/45d9cde9-2877-417e-b8bc-91193ce6aecb';
        assert.equal((await runLambda(testEvent)).status, '401');
    });

    it('500 from assets service - expects 500', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri =
            '/c9c47779-235f-4ca9-aefb-96758fa96650/978b666d-c456-4b1a-b57d-1c788d3c776b';
        assert.equal((await runLambda(testEvent)).status, '500');
    });

    // This will need some Read-only credential setup
    /*it('Valid HTTP request for streaming asset - expects pass', async function() {
        let testEvent = clone(testEventTemplate)
        testEvent.Records[0].cf.request.uri = "/c9c47779-235f-4ca9-aefb-96758fa96650/48ad03ab6e4aec392caba9921c0e1517_testChannel.m3u8";
        const resp = await runLambda(testEvent)
        assert.equal(resp.status, '200');
        assert.equal(resp.body.includes("#EXTM3U"), true);
        assert.equal(resp.body.includes("X-Amz-SignedHeaders=host&x-id=GetObject"), true);
    });*/
});

// Runs lambda function with provided event object
async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
