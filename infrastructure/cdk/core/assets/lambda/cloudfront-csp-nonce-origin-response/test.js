'use strict';
/*
 Unit tests to verify auth functions. Mostly consists of negative tests.
 Negative tests are used to ensure we don't accidentally allow to customer
 resources

 How to run:
    1- install dependencies `npm install`
    2- run local tests `npm test`
 */
const fs = require('fs');
const assert = require('assert').strict;
const https = require('https');
const lambdaLocal = require('lambda-local');
const path = require('path');
const testEventTemplate = require('./test-event.json');

describe('Test', function () {
    it('Valid http response with valid /index.html - expects 200', async function () {
        let testEvent = clone(testEventTemplate);
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), true);
        assert.ok((await runLambda(testEvent)).body.match(/nonce="[^"]*"/));
    });

    it('Valid http response with valid root valid html - expects 200', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/';
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), true);
        assert.ok((await runLambda(testEvent)).body.match(/nonce="[^"]*"/));
    });

    it('Valid http response with valid root valid html - expects 200', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dummyNonHTML';
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), false);
        assert.equal((await runLambda(testEvent)).body, 'NonHTMLResponseBody');
    });
});

// Runs lambda function with provided event object
async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 3,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
