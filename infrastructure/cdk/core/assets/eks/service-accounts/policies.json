{"base_service_permissions": [{"effect": "Allow", "actions": ["rds-db:connect"], "resources": ["arn:aws:rds-db:*:*:dbuser:*/postgres"]}, {"effect": "Allow", "actions": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "resources": ["arn:aws:secretsmanager:*:*:secret:redis-unblocked-password-*"]}, {"effect": "Allow", "actions": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "resources": ["arn:aws:secretsmanager:*:*:secret:activemq-unblocked-password-*"]}, {"effect": "Allow", "actions": ["dynamodb:BatchWriteItem", "dynamodb:PutItem"], "resources": ["arn:aws:dynamodb:*:*:table/sensitiveLogCache"]}], "bedrock_list_invoke_rag": [{"effect": "Allow", "actions": ["bedrock:CreateModelInvocationJob", "bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:ListFoundationModels", "bedrock:ListInferenceProfiles", "bedrock:<PERSON><PERSON>", "bedrock:RetrieveAndGenerate"], "resources": ["*"]}], "rds_read_write": [{"effect": "Allow", "actions": ["rds-db:connect"], "resources": ["arn:aws:rds-db:*:*:dbuser:*/postgres"]}], "redis_secret_read": [{"effect": "Allow", "actions": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "resources": ["arn:aws:secretsmanager:*:*:secret:redis-unblocked-password-*"]}], "activemq_secret_read": [{"effect": "Allow", "actions": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "resources": ["arn:aws:secretsmanager:*:*:secret:activemq-unblocked-password-*"]}], "dynamodb_read_sensitiveLogCache": [{"effect": "Allow", "actions": ["dynamodb:BatchGetItem", "dynamodb:GetItem"], "resources": ["arn:aws:dynamodb:*:*:table/sensitiveLogCache"]}], "dynamodb_read_write_mlRouterWebhookEventStore": [{"effect": "Allow", "actions": ["dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:BatchGetItem", "dynamodb:GetItem"], "resources": ["arn:aws:dynamodb:*:*:table/mlRouterWebhookEventStore"]}], "dynamodb_read_write_ciTriageCache": [{"effect": "Allow", "actions": ["dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:BatchGetItem", "dynamodb:GetItem"], "resources": ["arn:aws:dynamodb:*:*:table/ciTriageCache"]}], "dynamodb_read_rawIngestionData": [{"effect": "Allow", "actions": ["dynamodb:BatchGetItem", "dynamodb:GetItem"], "resources": ["arn:aws:dynamodb:*:*:table/rawIngestionData"]}], "dynamodb_read_write_rawIngestionData": [{"effect": "Allow", "actions": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:UpdateItem"], "resources": ["arn:aws:dynamodb:*:*:table/rawIngestionData", "arn:aws:dynamodb:*:*:table/rawIngestionData/index/*"]}], "lambda_invoke_functions": [{"effect": "Allow", "actions": ["lambda:InvokeFunction"], "resources": ["*"]}], "state_machine_start_stop_list_describe": [{"effect": "Allow", "actions": ["states:DescribeExecution", "states:ListExecutions", "states:ListStateMachines", "states:StartExecution", "states:StopExecution"], "resources": ["*"]}], "sagemaker_list_invoke_describe": [{"effect": "Allow", "actions": ["sagemaker:DescribeEndpoint", "sagemaker:InvokeEndpoint", "sagemaker:ListEndpoints"], "resources": ["*"]}], "s3_asanaservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_codaservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_confluenceservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_dataservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_jiraservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_jiraservice_read": [{"effect": "Allow", "actions": ["s3:GetObject", "s3:GetObjectAcl", "s3:AbortMultipartUpload", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:ListBucket"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_scmservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetBucketLocation", "s3:GetObject", "s3:GetObjectAcl", "s3:ListAllMyBuckets", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::pipeline-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/pipeline-*"]}}}], "s3_searchservice_read": [{"effect": "Allow", "actions": ["s3:GetObject", "s3:GetObjectAcl", "s3:GetBucketLocation", "s3:ListAllMyBuckets"], "resources": ["arn:aws:s3:::integration-data-*"]}, {"effect": "Allow", "actions": ["kms:Decrypt"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/integration-data-*"]}}}], "s3_assetservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::user-assets-*", "arn:aws:s3:::user-*-assets-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/user-assets-*", "alias/user-*-assets-*"]}}}], "s3_adminwebservice_read": [{"effect": "Allow", "actions": ["s3:GetBucketLocation", "s3:ListAllMyBuckets"], "resources": "*"}, {"effect": "Allow", "actions": ["*"], "resources": ["arn:aws:s3:::download-assets.*.getunblocked.com", "arn:aws:s3:::download-assets.*.getunblocked.com/*", "arn:aws:s3:::releases.*.getunblocked.com", "arn:aws:s3:::releases.*.getunblocked.com/*"]}], "s3_topicservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetBucketLocation", "s3:GetObject", "s3:GetObjectAcl", "s3:ListAllMyBuckets", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::pipeline-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/pipeline-*"]}}}], "s3_sourcecodeservice_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetBucketLocation", "s3:GetObject", "s3:GetObjectAcl", "s3:ListAllMyBuckets", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::pipeline-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/pipeline-*"]}}}], "s3_prefect_job_read_write": [{"effect": "Allow", "actions": ["s3:AbortMultipartUpload", "s3:DeleteObject", "s3:GetObject", "s3:GetObjectAcl", "s3:ListBucket", "s3:ListBucketMultipartUploads", "s3:ListMultipartUploadParts", "s3:PutObject"], "resources": ["arn:aws:s3:::pipeline-*"]}, {"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": ["alias/pipeline-*"]}}}], "kms_secretservice_decrypt_encrypt_genkey": [{"effect": "Allow", "actions": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*"], "resources": ["*"], "conditions": {"ForAnyValue:StringLike": {"kms:ResourceAliases": "alias/global-master-secrets-encryption-key*"}}}], "proxy_provider_aws_iam_sts_assume_external": [{"effect": "Allow", "actions": ["sts:<PERSON><PERSON>Role"], "resources": ["arn:aws:iam::*:role/UnblockedBotReadOnlyAccess", "arn:aws:iam::*:role/UnblockedAWSIntegration"]}, {"effect": "<PERSON><PERSON>", "actions": ["sts:<PERSON><PERSON>Role"], "resources": ["*"], "conditions": {"ForAnyValue:StringNotLike": {"aws:ResourceArn": ["arn:aws:iam::*:role/UnblockedBotReadOnlyAccess", "arn:aws:iam::*:role/UnblockedAWSIntegration"]}}}]}